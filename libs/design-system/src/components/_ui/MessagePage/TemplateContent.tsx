import React, { useState, useRef, useEffect } from 'react';
import { <PERSON><PERSON>all, Link, ArrowLeft } from 'lucide-react';
import { CDSSMessage } from '../../../@types/Message';
import MessageStatus from './MessageStatus';
import ChatTimer from '../chatTimer/ChatTimer';
import MessageContextMenu from '@cdss-modules/design-system/context/MessageContextMenu';
import { useMediaUrl } from '../../../lib/hooks/useMessageMediaProxy';

interface TemplateContentProps {
  message: CDSSMessage;
  isCustomer: boolean;
  setReferenceMessage?: (referenceMessage: CDSSMessage) => void;
  showContextMenu?: boolean;
}

const TemplateContent: React.FC<TemplateContentProps> = ({
  message,
  isCustomer,
  setReferenceMessage,
  showContextMenu = true,
}) => {
  const [imageRatio, setImageRatio] = useState<number | null>(null);
  const imageRef = useRef<HTMLImageElement>(null);
  const { transformMediaUrl } = useMediaUrl();
  const originalMedia = message.medias?.[0];
  const media = transformMediaUrl(originalMedia);

  useEffect(() => {
    console.log('template message content: ', message);
  }, [message]);

  const templateData = React.useMemo(() => {
    try {
      return JSON.parse(message.textBody);
    } catch (error) {
      console.error('Failed to parse template data:', error);
      return null;
    }
  }, [message.textBody]);

  if (!templateData) return null;

  const formatText = (text: string) => {
    if (!text) return '';

    return text
      .replace(/\*(.*?)\*/g, '<strong>$1</strong>')
      .replace(/_(.*?)_/g, '<em>$1</em>')
      .replace(/~(.*?)~/g, '<del>$1</del>')
      .replace(/```(.*?)```/g, '<code>$1</code>')
      .replace(/\n/g, '<br/>');
  };

  const handleImageLoad = () => {
    const img = imageRef.current;
    if (img) {
      const ratio = img.width / img.height;
      setImageRatio(ratio);
    }
  };

  const renderFormattedText = (text: string) => {
    return <div dangerouslySetInnerHTML={{ __html: formatText(text) }} />;
  };

  const renderButton = (button: any) => {
    switch (button.type) {
      case 'PHONE_NUMBER':
        return (
          <button className="w-full flex items-center justify-start text-blue-500 hover:text-blue-600 py-2">
            <PhoneCall className="w-4 h-4 mr-2" />
            <span>{button.text}</span>
          </button>
        );
      case 'URL':
        return (
          <div className="w-full text-blue-500 hover:text-blue-600 py-2">
            <Link className="w-4 h-4 inline-block mr-2" />
            <a
              href={button.url}
              target="_blank"
              rel="noopener noreferrer"
              className="hover:underline"
            >
              {button.text}
            </a>
          </div>
        );
      case 'QUICK_REPLY':
        return (
          <button className="w-full flex items-center justify-start text-blue-500 hover:text-blue-600 py-2">
            <ArrowLeft className="w-4 h-4 mr-2" />
            <span>{button.text}</span>
          </button>
        );
      default:
        return null;
    }
  };

  const containerClasses = isCustomer
    ? 'm-2 rounded-lg bg-[#262626] text-white border border-gray-700'
    : 'm-2 rounded-lg bg-white shadow-sm border border-gray-200';

  return (
    <div className="mx-4 my-2 flex flex-col">
      <MessageContextMenu
        message={message}
        setReferenceMessage={setReferenceMessage}
        disabled={!showContextMenu}
      >
        <div className={`${containerClasses}`}>
          {/* Header */}
          {templateData.components.map((component: any, index: number) => {
            if (component.type === 'HEADER') {
              if (component.format === 'TEXT') {
                return (
                  <div
                    key={index}
                    className="text-lg px-3 pt-3"
                  >
                    {component.text}
                  </div>
                );
              } else if (component.format === 'IMAGE') {
                const imageUrl = media?.url;
                return imageUrl ? (
                  <div
                    key={index}
                    className="w-full"
                  >
                    <img
                      ref={imageRef}
                      src={imageUrl}
                      alt="Header"
                      onLoad={handleImageLoad}
                      className={`w-full rounded-t-lg object-cover ${
                        imageRatio !== null && imageRatio <= 1
                          ? 'max-h-96' // 纵向或正方形图片
                          : 'max-h-48' // 横向图片
                      }`}
                    />
                  </div>
                ) : null;
              }
            }
            return null;
          })}

          {/* Body */}
          {templateData.components.map((component: any, index: number) => {
            if (component.type === 'BODY') {
              return (
                <div
                  key={index}
                  className="px-3 py-2"
                >
                  {renderFormattedText(component.text || '')}
                </div>
              );
            }
            return null;
          })}

          {/* Footer */}
          {templateData.components.map((component: any, index: number) => {
            if (component.type === 'FOOTER') {
              return (
                <div
                  key={index}
                  className="px-3 pb-2 text-sm opacity-70"
                >
                  {component.text}
                </div>
              );
            }
            return null;
          })}

          {/* Buttons */}
          {templateData.components.map((component: any, index: number) => {
            if (component.type === 'BUTTONS' && component.buttons?.length > 0) {
              return (
                <div
                  key={index}
                  className={`border-t mt-2 ${isCustomer ? 'border-gray-700' : 'border-gray-200'}`}
                >
                  {component.buttons.map((button: any, buttonIndex: number) => (
                    <div
                      key={buttonIndex}
                      className={`px-3 py-2 border-b last:border-b-0 ${
                        isCustomer ? 'border-gray-700' : 'border-gray-200'
                      }`}
                    >
                      {renderButton(button)}
                    </div>
                  ))}
                </div>
              );
            }
            return null;
          })}
        </div>
      </MessageContextMenu>

      {/* Timestamp and Status */}
      <div
        className={`flex items-center space-x-1 px-1 mt-1 ${
          isCustomer ? 'self-start' : 'self-end'
        }`}
      >
        <ChatTimer
          timestamp={message.timestamp}
          className="text-xs text-gray-500"
        />
        {!isCustomer && message.status && (
          <MessageStatus status={message.status} />
        )}
      </div>
    </div>
  );
};

export default TemplateContent;
