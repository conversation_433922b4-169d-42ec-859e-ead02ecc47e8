import { FC, memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import {
    <PERSON><PERSON>,
    OnBehalfOf,
    Ta<PERSON>,
    TabsContent,
    toast,
    Tooltip,
    useRole,
    useTabsContext,
    useToast,
} from '@cdss-modules/design-system';
import { AGENT_STATUS_COLOR_MAP } from '@cdss-modules/design-system/lib/constants/status';
import { cn, removePhoneNumberSpaces, validateGlobalPhoneNumber } from '@cdss-modules/design-system/lib/utils';
import CallControlButton from '@cdss-modules/design-system/components/_ui/CallControlButton';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from '@cdss-modules/design-system/components/_ui/DropdownMenu';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import Pill from '@cdss-modules/design-system/components/_ui/Pill';
import NewConversationContainer from '@cdss-modules/design-system/components/_ui/CallBack/NewConversation';
import { useOpenStationContext } from '@cdss-modules/design-system/context/StationContext';
import { useTbarContext } from '@cdss-modules/design-system/context/TBarContext';
import { useCallControl } from '@cdss-modules/design-system/lib/hooks/useCallControl';
import { useGetworkGroupOrUser } from '@cdss-modules/design-system/lib/hooks/useGetworkGroupOrUser';
import { useHandleInteractionData } from '@cdss-modules/design-system/lib/hooks/useHandleInteractionData';
import { TContact, TWorkgroup } from '@cdss-modules/design-system/@types/Interaction';
type TProps = {
    children: React.ReactNode,
    setOpenSeletedStation?: (v: boolean) => void,
    open?: boolean,
    setOpenDirectory: (v: boolean) => void,
    title?: string,
    disabled?: boolean,
    isShowButton?: boolean,
    searchValue?: any,
    setSearch?: (v: any) => void,
    getSelectValue?: (v: any) => void
};
const Directory: FC<TProps> = memo(({ setOpenSeletedStation, open, setOpenDirectory, children, title = "Directory", disabled = false, isShowButton = true, searchValue, setSearch, getSelectValue }) => {
    const triggers = [
        {
            value: 'user',
            label: 'User',
        },
        {
            value: 'workgroup',
            label: 'Workgroup',
        },
    ];
    const inputRef = useRef<HTMLInputElement>(null);
    const { setOpen, setOpenOption } = useOpenStationContext();
    const { selected: currentTab } = useTabsContext();
    const { call, consult, transfer } = useCallControl();
    const { userConfig } = useRole();
    const [inputValue, setInputValue] = useState('');
    const [selected, setSelected] = useState<any | null>(null);
    const [isPersonSelect, setIsPersonSelect] = useState<boolean>(false);
    const [showModal, setShowModal] = useState(false);
    const {
        handleAfterCallFunc,
        selectedInteraction,
        stationContext: {
            station,
            stationHandler
        }
    } = useTbarContext();
    const {
        directorySearchHandler,
        getAllDirectoryHandler,
        getAllWorkgroupHandler,
        directorySearchKeyword,
        setDirectorySearchKeyword,
        setWorkgroupSearchKeyword,
        workgroupSearchHandler,
        workgroupSearchKeyword,
    } = useGetworkGroupOrUser();
    const { conversationId, agentData: agent, consultData, customerData } =
        useHandleInteractionData(selectedInteraction);

    const customerParticipantId = customerData?.id || consultData?.id || '';

    const filteredDiretoryList = useMemo(() => {
        const directoryList = directorySearchKeyword
            ? directorySearchHandler?.data
            : getAllDirectoryHandler?.data;

        // 当输入值重新查询时，将 selected 赋值为空
        if (directorySearchKeyword && !isPersonSelect) {
            setSelected(null);
        }

        // 选择最符合查询条件的第一条数据并 setSelected
        // if (directorySearchKeyword && directoryList && directoryList.length > 0 && (!selected || !isPersonSelect)) {
        //     getSelectValue && getSelectValue(directoryList[0])
        //     setSelected({ ...directoryList[0], isQueue: false });
        // }

        return directoryList?.map((item) => {
            return {
                ...item,
                isQueue: false,
            };
        }) || [];
    }, [
        directorySearchHandler?.data,
        getAllDirectoryHandler?.data,
        directorySearchKeyword,
        isPersonSelect,
    ]);


    const filteredWorkgroupList = useMemo(() => {
        const workgroupList = workgroupSearchKeyword
            ? workgroupSearchHandler?.data || []
            : getAllWorkgroupHandler?.data;

        // 当输入值重新查询时，将 selected 赋值为空
        if (workgroupSearchKeyword && !isPersonSelect) {
            setSelected(null);
        }

        // 选择最符合查询条件的第一条数据并 setSelected
        // if (workgroupSearchKeyword && workgroupList && workgroupList.length > 0 && (!selected || !isPersonSelect)) {
        //     getSelectValue && getSelectValue(workgroupList[0])
        //     setSelected({ ...workgroupList[0], isQueue: true });
        // }

        return workgroupList?.map((item) => {
            return {
                ...item,
                isQueue: true,
            };
        }) || [];
    }, [
        workgroupSearchHandler?.data,
        getAllWorkgroupHandler?.data,
        workgroupSearchKeyword,
        isPersonSelect,
    ]);


    const search = useCallback(
        (v: string) => {
            setSelected(null)
            setInputValue(v);

            if ((v || v !== '') && currentTab?.includes('user')) {
                setDirectorySearchKeyword(v)
            }
            if ((!v || v === '') && currentTab?.includes('user')) {
                setDirectorySearchKeyword('')
            }
            if ((v || v !== '') && currentTab?.includes('workgroup')) {
                setWorkgroupSearchKeyword(v)
            }
            if ((!v || v === '') && currentTab?.includes('workgroup')) {
                setWorkgroupSearchKeyword('')
            }
        },
        [currentTab]
    );

    const selectGroup = (person: TContact | TWorkgroup) => {
        getSelectValue && getSelectValue(person)
        setSearch && setSearch(person?.name)
        setSearch && setOpenDirectory(false)
        setSelected(person)
        setInputValue(person?.name)
        setIsPersonSelect(true)

    };
    const { dismiss } = useToast();
    const handleToastClose = () => {
        dismiss();
    };
    const selectStationTip = () => {
        return toast({
            variant: 'error',
            title: 'Warning',
            description: (
                <div className="flex flex-col w-full gap-2">
                    <div>You have no phone selected and will not receive calls.</div>
                    <div className="flex w-full gap-2">
                        <Button
                            variant={'primary'}
                            size={'s'}
                            onClick={() => {
                                setOpenOption(true)
                                setOpen(true)
                                // setOpenSeletedStation && setOpenSeletedStation(true)
                                handleToastClose();
                                stationHandler?.refetch();
                            }}
                        >
                            Select Phone
                        </Button>
                        <Button
                            variant={'blank'}
                            size={'s'}
                            onClick={() => {
                                handleToastClose();
                            }}
                        >
                            Close
                        </Button>
                    </div>
                </div>
            ),
        });
    };
    const onOpenChange = useCallback((v: boolean) => {
        setIsPersonSelect(false)
        if (v) {
            getAllDirectoryHandler?.refetch();
        }
        setOpenDirectory(v)

    }, [station])

    const makeCallByDifferentKey = useCallback(() => {
        if (selected) {
            if (selected?.presence) {
                // call user
                return {
                    callUserId: selected?.id,
                };
            } else {
                // call queue
                return {
                    callQueueId: selected?.id,
                    phoneNumber: removePhoneNumberSpaces(selected?.phoneNumber),
                };
            }
        } else {
            // call number
            return {
                phoneNumber: removePhoneNumberSpaces(inputValue),
            };
        }
    }, [inputValue, selected]);
    const makeConsultCallByDifferentKey = () => {
        if (selected) {
            if (selected?.status) {
                return {
                    destinationUserId: selected?.id,
                };
            } else {
                return {
                    destinationQueueId: selected?.id,
                    destinationAddress: removePhoneNumberSpaces(selected?.phoneNumber),
                };
            }
        } else {
            return { destinationAddress: removePhoneNumberSpaces(inputValue) };
        }
    };
    const transferRender = (person: TContact) => {
        return (
            <>
                <CallControlButton
                    tooltip={'Consult'}
                    icon={
                        <Icon
                            name="outbound"
                            size={16}
                            className="text-white"
                        />
                    }
                    className={cn(
                        'flex-none size-8 bg-status-info disabled:bg-grey-400 hover:border-none'
                    )}
                    onClick={() => {
                        if (!station || station?.name === "") {
                            selectStationTip()
                            return
                        }
                        consult(
                            conversationId,
                            customerParticipantId,
                            makeConsultCallByDifferentKey()
                        );
                        onOpenChange(false)
                    }}
                    disabled={(inputValue === '' && !selected)
                    }
                />
                <CallControlButton
                    tooltip={'Blind'}
                    icon={
                        <Icon
                            name="transfer"
                            size={16}
                            className="text-white"
                        />
                    }
                    className={cn(
                        'flex-none size-8 bg-status-info disabled:bg-grey-400 hover:border-none'
                    )}
                    onClick={() => {
                        if (!station || station?.name === "") {
                            selectStationTip()
                            return
                        }
                        transfer(
                            conversationId,
                            agent.id || '',
                            makeConsultCallByDifferentKey()
                        );
                        onOpenChange(false)
                    }}
                    disabled={(inputValue === '' && !selected) || person?.presence?.presenceDefinition?.systemPresence !== "On Queue"
                    }
                />
            </>
        )
    }

    useEffect(() => {
        if (!inputValue || inputValue === "") {
            setSelected(null)
        }
    }, [inputValue, searchValue])

    useEffect(() => {
        inputRef?.current?.focus();
        if (searchValue && searchValue !== "") {
            setInputValue(searchValue)
            if (currentTab?.includes('user')) {
                setDirectorySearchKeyword(searchValue)
            }
            if (currentTab?.includes('workgroup')) {
                setWorkgroupSearchKeyword(searchValue)
            }
            return
        }
        setInputValue("")
        setWorkgroupSearchKeyword('')
        setDirectorySearchKeyword('')
        setSelected(null)
    }, [currentTab, open, searchValue])
    return (
        <>
            <DropdownMenu
                open={open}
                onOpenChange={(open) => onOpenChange(open)}
            >
                <Tooltip
                    content={title}
                    trigger={
                        <DropdownMenuTrigger
                            className={cn("hover:text-primary text-[#5B5E63] ", disabled && "cursor-no-drop")}
                            onClick={() => { }}
                            disabled={disabled}
                        >
                            {children}
                        </DropdownMenuTrigger>}
                />
                <DropdownMenuContent
                    side="top"
                    arrowPadding={2}
                    className="overflow-visible"
                >
                    <div className="relative p-2">
                        <div className="pb-3">
                            <strong>{title}</strong>
                        </div>
                        <div className='flex gap-2'>
                            <Input
                                ref={inputRef}
                                type="text"
                                isSearch={true}
                                afterIcon={<Icon name="search" />}
                                afterIconFn={() => console.log('click after')}
                                onChange={(value: any) => { search(value); setSearch && setSearch(value) }}
                                allowClear
                                size="s"
                                placeholder="Search..."
                                value={inputValue || searchValue}
                            />
                            <OnBehalfOf
                                // triggerClassName={triggerClassName}
                                children={
                                    <>
                                        {title == "Directory" && isShowButton && <CallControlButton
                                            tooltip={'Dial'}
                                            icon={
                                                <Icon
                                                    name="phone"
                                                    size={16}
                                                    className="text-white"
                                                />
                                            }
                                            className={cn(
                                                'flex-none size-8 bg-status-success disabled:bg-grey-400 hover:border-none'
                                            )}
                                            onClick={() => {
                                                setShowModal(true)
                                            }}
                                            disabled={!inputValue && !selected}
                                        />}
                                    </>
                                }
                                content={
                                    <NewConversationContainer
                                        returnEvent={() => {
                                            setShowModal(false)
                                            setOpenDirectory(false)
                                        }}
                                        phoneNumber={makeCallByDifferentKey()}
                                        station={station}
                                        stationHandler={stationHandler}


                                    />
                                }

                                onOpenChange={(v) => {
                                    setShowModal(v)
                                }
                                }
                                disabled={!inputValue && !selected}
                                open={showModal}
                                title="On behalf of"
                            />

                            {title == "Transfer" && transferRender(selected)}
                        </div>

                        <Tabs
                            defaultTab="user"
                            triggers={triggers}
                            triggerClassName="p-0 py-2"
                        >
                            <TabsContent
                                value={'user'}
                                className="p-0 py-2 relative bg-white h-[290px] overflow-y-auto"
                            >
                                {filteredDiretoryList.map((person: TContact) => {
                                    return (
                                        <Pill
                                            variant="person"
                                            key={person.id}
                                            onClick={() => selectGroup(person)}
                                            active={selected?.id === person.id}
                                            disabled={person?.id === userConfig?.id}
                                            className="border-none  w-full"
                                        >
                                            <div className="flex items-center gap-x-4 pointer-events-auto w-full">
                                                <div className="flex flex-row w-full justify-between">
                                                    <div className='flex flex-col items-start'>
                                                        <div className="flex gap-1 items-center">
                                                            <div
                                                                style={{
                                                                    background:
                                                                        AGENT_STATUS_COLOR_MAP[
                                                                        person?.presence?.presenceDefinition?.systemPresence?.toLowerCase()
                                                                        ],
                                                                }}
                                                                className={`size-[10px] rounded-full`}
                                                            />
                                                            <div className="text-remark">{person.name}</div>
                                                        </div>
                                                        <div className="italic text-footnote text-grey-500">
                                                            {person?.addresses?.[0]?.address?.slice(4)}
                                                        </div>
                                                    </div>
                                                    <div className='flex gap-2'>
                                                        {selected?.id === person.id && title == "Transfer" && transferRender(person)}
                                                    </div>

                                                </div>
                                            </div>
                                        </Pill>
                                    );
                                })}
                                {filteredDiretoryList.length === 0 &&
                                    !getAllDirectoryHandler?.isFetching &&
                                    !directorySearchHandler?.isLoading && (
                                        <div className="text-remark w-[250px]">
                                            No matching result(s) in directory. Click the dial icon or
                                            press enter to call{' '}
                                            <strong className="text-primary">{inputValue}</strong>.
                                        </div>
                                    )}
                            </TabsContent>
                            <TabsContent
                                value={'workgroup'}
                                className="p-0 py-2 relative bg-white h-[290px] overflow-y-auto"
                            >
                                {filteredWorkgroupList?.map((person: TWorkgroup) => {
                                    return (
                                        <Pill
                                            variant="person"
                                            key={person.id}
                                            onClick={() => selectGroup(person)}
                                            active={selected?.id === person.id}
                                            className="border-none  w-full"
                                        >
                                            <div className="flex items-center gap-x-4 pointer-events-auto w-full">
                                                <div className="flex flex-col items-start">
                                                    <div className="flex gap-1 items-center">
                                                        <div className="size-[10px] rounded-full bg-status-success" />
                                                        <div className="text-remark">{person.name}</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </Pill>
                                    );
                                })}
                                {filteredWorkgroupList?.length === 0 &&
                                    !getAllWorkgroupHandler?.isFetching && (
                                        <div className="text-remark w-[250px]">
                                            No matching result(s) in workgroup. Click the dial icon or
                                            press enter to call{' '}
                                            <strong className="text-[#5B5E63]">{inputValue}</strong>.
                                        </div>
                                    )}
                            </TabsContent>
                        </Tabs>
                    </div>
                </DropdownMenuContent>
            </DropdownMenu>
        </>
    );
});
Directory.displayName = 'Directory';
export default Directory;