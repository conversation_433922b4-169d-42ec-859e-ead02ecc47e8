import React from 'react';

interface IconProps {
    size?: string;
    alt?: string;
    className?: string;
}

const IconHoldCustomer: React.FC<IconProps> = ({ size, alt = "Hold User Icon", className }) => {
    return (
        <svg width="81" height="76" viewBox="0 0 81 76" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="38" cy="38" r="37.5" fill="#EBEBEB" />
            <path d="M44.7673 40H32.7673C31.9829 40 31.8139 40.6154 32.0188 41.122C32.1322 41.4025 32.3483 41.6295 32.5087 41.8861L36.3665 48.0587C36.9421 48.9797 37.1185 50.0952 36.8551 51.1489L34.2834 61.4356C33.9607 62.7263 34.3008 64.0928 35.1908 65.0816L35.4469 65.3662C36.98 67.0697 39.628 67.1393 41.2485 65.5188L41.6658 65.1014C42.6594 64.1079 43.0588 62.666 42.718 61.3029L40.1964 51.2166C39.9231 50.1233 40.1222 48.9658 40.7612 48.0375C42.5586 45.4268 45.0574 41.9196 45.2673 41.5C45.7673 40.5 45.2673 40 44.7673 40Z" fill="url(#paint0_linear_7777_21632)" />
            <circle cx="38.7673" cy="23" r="14.5" fill="url(#paint1_linear_7777_21632)" />
            <path d="M29.1136 67.9883C23.6032 67.9886 6.18812 67.9901 5.76733 68C5.26729 68.0117 4.76729 57.4766 11.2673 49.9883C15.5987 44.9983 21.0403 42.4454 24.189 41.2974C25.7237 40.7378 27.3722 41.3831 28.2954 42.7307L32.2045 48.4369C32.8777 49.4196 33.0779 50.6511 32.7507 51.7964L29.813 62.0784C29.4692 63.2816 29.7008 64.5825 30.4811 65.5607C30.8029 65.9641 31.0979 66.3189 31.2673 66.4883C31.2847 66.5058 31.3004 66.5239 31.3142 66.5427C31.9444 67.3961 30.1745 67.9883 29.1136 67.9883Z" fill="url(#paint2_linear_7777_21632)" />
            <path d="M47.421 67.9883C52.9315 67.9886 70.3466 67.9901 70.7673 68C71.2674 68.0117 71.7674 57.4766 65.2674 49.9883C60.9359 44.9983 55.4944 42.4454 52.3456 41.2974C50.8109 40.7378 49.1625 41.3831 48.2393 42.7307L44.3302 48.4369C43.657 49.4196 43.4568 50.6511 43.784 51.7964L46.7217 62.0784C47.0655 63.2816 46.8339 64.5825 46.0536 65.5607C45.7318 65.9641 45.4368 66.3189 45.2674 66.4883C45.2499 66.5058 45.2343 66.5239 45.2204 66.5427C44.5903 67.3961 46.3601 67.9883 47.421 67.9883Z" fill="url(#paint3_linear_7777_21632)" />
            <circle opacity="0.8" cx="65" cy="58" r="14.5" fill="#C6C6C6" stroke="white" stroke-width="2" />
            <path d="M66.8047 59.803C65.1226 61.4853 63.1738 63.0941 62.4043 62.3246C61.3002 61.2222 60.6219 60.2612 58.1918 62.2154C55.7616 64.1697 57.6281 65.4726 58.6964 66.541C59.9294 67.7722 64.5231 66.6072 69.0649 62.0651C73.6049 57.523 74.7735 52.9291 73.5405 51.696C72.4721 50.6276 71.1712 48.761 69.217 51.1931C67.2628 53.6235 68.2238 54.3017 69.3262 55.4059C70.0939 56.1719 68.4869 58.1208 66.8047 59.803Z" fill="white" />
            <defs>
                <linearGradient id="paint0_linear_7777_21632" x1="38.7067" y1="40" x2="38.7067" y2="68.5" gradientUnits="userSpaceOnUse">
                    <stop stop-color="#BBBEC3" />
                    <stop offset="1" stop-color="#90939C" />
                </linearGradient>
                <linearGradient id="paint1_linear_7777_21632" x1="38.7673" y1="8.5" x2="38.7673" y2="37.5" gradientUnits="userSpaceOnUse">
                    <stop stop-color="#BDC1C5" />
                    <stop offset="1" stop-color="#90939C" />
                </linearGradient>
                <linearGradient id="paint2_linear_7777_21632" x1="19.3836" y1="40.5" x2="19.3836" y2="68" gradientUnits="userSpaceOnUse">
                    <stop stop-color="#BBBEC3" />
                    <stop offset="1" stop-color="#90939C" />
                </linearGradient>
                <linearGradient id="paint3_linear_7777_21632" x1="57.151" y1="40.5" x2="57.151" y2="68" gradientUnits="userSpaceOnUse">
                    <stop stop-color="#BBBEC3" />
                    <stop offset="1" stop-color="#90939C" />
                </linearGradient>
            </defs>
        </svg>
    );
};

export default IconHoldCustomer;
