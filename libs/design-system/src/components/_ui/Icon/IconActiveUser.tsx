import React from 'react';

interface IconProps {
    size?: string;
    alt?: string;
    className?: string;
    viewBox?: string;
}

const IconActiveUser: React.FC<IconProps> = ({ size, alt, className, viewBox }) => {
    return (
        <svg width="87" height="80" viewBox="0 0 87 80" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M70.8889 44.4298C70.8889 64.0052 55.0199 79.8742 35.4444 79.8742C15.869 79.8742 0 64.0052 0 44.4298C0 24.8544 15.869 8.98535 35.4444 8.98535C55.0199 8.98535 70.8889 24.8544 70.8889 44.4298Z" fill="#FFAC4A" />
            <rect x="5.63892" y="8.18066" width="37.8611" height="45.9167" rx="4" fill="#F6F6F6" />
            <g clip-path="url(#clip0_7757_21658)">
                <ellipse cx="51.5556" cy="13.8191" rx="13.2917" ry="10.3571" fill="white" />
                <path d="M49.4325 0.93142C51.3227 0.913773 53.0987 1.20495 54.7604 1.80495C56.4221 2.40495 57.8813 3.22113 59.1379 4.25349C60.3946 5.28584 61.3864 6.50349 62.1134 7.90644C62.8404 9.30938 63.2143 10.8138 63.2351 12.4197C63.2558 13.9373 62.9495 15.3668 62.3159 16.7079C61.6824 18.0491 60.7996 19.2359 59.6676 20.2682C58.5356 21.3006 57.2062 22.1521 55.6795 22.8227C54.1528 23.4932 52.5067 23.908 50.7411 24.0668C50.2841 24.1197 49.796 24.1638 49.2767 24.1991C48.7574 24.2344 48.181 24.2609 47.5475 24.2785C46.914 24.2962 46.2078 24.2962 45.4288 24.2785C44.6499 24.2609 43.7723 24.208 42.7961 24.1197C40.6359 23.9432 39.0313 23.7138 37.9823 23.4315C36.9334 23.1491 36.5127 22.9903 36.7205 22.955C37.8213 22.7962 38.7664 22.5403 39.5557 22.1874C39.9712 21.9932 40.1425 21.7153 40.0698 21.3535C39.9971 20.9918 39.7946 20.6521 39.4623 20.3344C38.4029 19.3462 37.5461 18.1947 36.8918 16.88C36.2375 15.5653 35.8896 14.1491 35.8481 12.6315C35.8273 11.0256 36.17 9.51674 36.8762 8.10497C37.5825 6.6932 38.5483 5.4579 39.7738 4.39908C40.9994 3.34025 42.443 2.50201 44.1047 1.88436C45.7664 1.26672 47.5423 0.949067 49.4325 0.93142ZM55.5081 12.5785C55.5081 13.0373 55.6951 13.43 56.069 13.7565C56.4428 14.0829 56.8998 14.2462 57.4399 14.2462C57.9799 14.2462 58.4421 14.0829 58.8264 13.7565C59.2106 13.43 59.4028 13.0373 59.4028 12.5785C59.4028 12.1197 59.2106 11.7314 58.8264 11.4138C58.4421 11.0962 57.9799 10.9373 57.4399 10.9373C56.8998 10.9373 56.4428 11.0962 56.069 11.4138C55.6951 11.7314 55.5081 12.1197 55.5081 12.5785ZM47.6877 12.5785C47.6877 13.0373 47.8851 13.4344 48.2797 13.7697C48.6744 14.105 49.1417 14.2726 49.6818 14.2726C50.2426 14.2726 50.7151 14.105 51.0994 13.7697C51.4837 13.4344 51.6758 13.0373 51.6758 12.5785C51.6758 12.102 51.4837 11.7006 51.0994 11.3741C50.7151 11.0476 50.2426 10.8844 49.6818 10.8844C49.1417 10.8844 48.6744 11.0476 48.2797 11.3741C47.8851 11.7006 47.6877 12.102 47.6877 12.5785ZM39.9608 12.552C39.9608 13.0109 40.1529 13.4079 40.5372 13.7432C40.9215 14.0785 41.3836 14.2462 41.9237 14.2462C42.4845 14.2462 42.957 14.0785 43.3413 13.7432C43.7256 13.4079 43.9177 13.0109 43.9177 12.552C43.9177 12.0932 43.7256 11.7006 43.3413 11.3741C42.957 11.0476 42.4845 10.8844 41.9237 10.8844C41.3836 10.8844 40.9215 11.0476 40.5372 11.3741C40.1529 11.7006 39.9608 12.0932 39.9608 12.552Z" fill="#1CC500" />
            </g>
            <rect x="12.8889" y="22.6807" width="23.3611" height="4.02778" rx="2.01389" fill="#FFAC4A" />
            <rect x="12.8889" y="37.1807" width="12.8889" height="4.02778" rx="2.01389" fill="#FFAC4A" />
            <rect x="12.8889" y="29.9307" width="23.3611" height="4.02778" rx="2.01389" fill="#FFAC4A" />
            <path d="M30.2788 39.443C30.7099 37.6438 32.3186 36.375 34.1688 36.375H81.9796C84.5495 36.375 86.4526 38.7636 85.8786 41.2685L78.8508 71.9352C78.4342 73.753 76.8168 75.0417 74.9519 75.0417H26.8215C24.2354 75.0417 22.3291 72.6246 22.9316 70.1097L30.2788 39.443Z" fill="url(#paint0_linear_7757_21658)" />
            <circle cx="54.375" cy="56.1111" r="5.23611" fill="#F3F3F3" />
            <defs>
                <linearGradient id="paint0_linear_7757_21658" x1="54.375" y1="36.375" x2="54.375" y2="75.0417" gradientUnits="userSpaceOnUse">
                    <stop stop-color="#FFF5DA" />
                    <stop offset="1" stop-color="#FFCA8C" />
                </linearGradient>
                <clipPath id="clip0_7757_21658">
                    <rect width="30.6111" height="25.7778" fill="white" transform="translate(34.2361 0.125)" />
                </clipPath>
            </defs>
        </svg>


    );
};

export default IconActiveUser;
