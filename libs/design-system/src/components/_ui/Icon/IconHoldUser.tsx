import React from 'react';

interface IconProps {
    size?: string;
    alt?: string;
    className?: string;
    viewBox?: string;
}

const IconHoldUser: React.FC<IconProps> = ({ size, alt, className, viewBox }) => {
    return (
        <svg width="89" height="80" viewBox="0 0 89 80" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M73 43.4375C73 63.5959 56.6584 79.9375 36.5 79.9375C16.3416 79.9375 0 63.5959 0 43.4375C0 23.2791 16.3416 6.9375 36.5 6.9375C56.6584 6.9375 73 23.2791 73 43.4375Z" fill="url(#paint0_linear_7750_93934)" />
            <rect x="7.63892" y="7" width="37.8611" height="45.9167" rx="4" fill="url(#paint1_linear_7750_93934)" />
            <g clip-path="url(#clip0_7750_93934)">
                <ellipse cx="58.3195" cy="13.7566" rx="13.2917" ry="10.3571" fill="white" />
                <path d="M56.1964 0.86892C58.0866 0.851273 59.8626 1.14245 61.5243 1.74245C63.186 2.34245 64.6452 3.15863 65.9018 4.19099C67.1585 5.22334 68.1503 6.44099 68.8773 7.84394C69.6043 9.24688 69.9782 10.7513 69.999 12.3572C70.0198 13.8748 69.7134 15.3043 69.0799 16.6454C68.4463 17.9866 67.5635 19.1734 66.4315 20.2057C65.2995 21.2381 63.9701 22.0896 62.4434 22.7602C60.9167 23.4307 59.2706 23.8455 57.505 24.0043C57.0481 24.0572 56.5599 24.1013 56.0406 24.1366C55.5214 24.1719 54.945 24.1984 54.3114 24.216C53.6779 24.2337 52.9717 24.2337 52.1928 24.216C51.4138 24.1984 50.5362 24.1455 49.56 24.0572C47.3998 23.8807 45.7952 23.6513 44.7462 23.369C43.6973 23.0866 43.2767 22.9278 43.4844 22.8925C44.5853 22.7337 45.5303 22.4778 46.3197 22.1249C46.7351 21.9307 46.9064 21.6528 46.8337 21.291C46.761 20.9293 46.5585 20.5896 46.2262 20.2719C45.1668 19.2837 44.31 18.1322 43.6557 16.8175C43.0014 15.5028 42.6535 14.0866 42.612 12.569C42.5912 10.9631 42.9339 9.45424 43.6402 8.04247C44.3464 6.6307 45.3122 5.3954 46.5378 4.33658C47.7633 3.27775 49.2069 2.43951 50.8686 1.82186C52.5303 1.20422 54.3062 0.886567 56.1964 0.86892ZM62.272 12.516C62.272 12.9748 62.459 13.3675 62.8329 13.694C63.2068 14.0204 63.6637 14.1837 64.2038 14.1837C64.7438 14.1837 65.206 14.0204 65.5903 13.694C65.9745 13.3675 66.1667 12.9748 66.1667 12.516C66.1667 12.0572 65.9745 11.6689 65.5903 11.3513C65.206 11.0337 64.7438 10.8748 64.2038 10.8748C63.6637 10.8748 63.2068 11.0337 62.8329 11.3513C62.459 11.6689 62.272 12.0572 62.272 12.516ZM54.4516 12.516C54.4516 12.9748 54.649 13.3719 55.0436 13.7072C55.4383 14.0425 55.9056 14.2101 56.4457 14.2101C57.0065 14.2101 57.4791 14.0425 57.8633 13.7072C58.2476 13.3719 58.4397 12.9748 58.4397 12.516C58.4397 12.0395 58.2476 11.6381 57.8633 11.3116C57.4791 10.9851 57.0065 10.8219 56.4457 10.8219C55.9056 10.8219 55.4383 10.9851 55.0436 11.3116C54.649 11.6381 54.4516 12.0395 54.4516 12.516ZM46.7247 12.4895C46.7247 12.9484 46.9168 13.3454 47.3011 13.6807C47.6854 14.016 48.1475 14.1837 48.6876 14.1837C49.2484 14.1837 49.721 14.016 50.1052 13.6807C50.4895 13.3454 50.6816 12.9484 50.6816 12.4895C50.6816 12.0307 50.4895 11.6381 50.1052 11.3116C49.721 10.9851 49.2484 10.8219 48.6876 10.8219C48.1475 10.8219 47.6854 10.9851 47.3011 11.3116C46.9168 11.6381 46.7247 12.0307 46.7247 12.4895Z" fill="url(#paint2_linear_7750_93934)" />
            </g>
            <rect x="14.8889" y="19" width="23.3611" height="4.02778" rx="2.01389" fill="white" />
            <rect x="14.8889" y="33.5" width="12.8889" height="4.02778" rx="2.01389" fill="white" />
            <rect x="14.8889" y="26.25" width="23.3611" height="4.02778" rx="2.01389" fill="white" />
            <path d="M32.2788 39.3805C32.7099 37.5813 34.3186 36.3125 36.1688 36.3125H83.9796C86.5495 36.3125 88.4526 38.7011 87.8786 41.206L80.8508 71.8727C80.4342 73.6905 78.8168 74.9792 76.9519 74.9792H28.8215C26.2354 74.9792 24.3291 72.5621 24.9316 70.0472L32.2788 39.3805Z" fill="url(#paint3_linear_7750_93934)" />
            <circle cx="56.375" cy="56.0486" r="5.23611" fill="#F3F3F3" />
            <defs>
                <linearGradient id="paint0_linear_7750_93934" x1="36.5" y1="6.9375" x2="36.5" y2="79.9375" gradientUnits="userSpaceOnUse">
                    <stop stop-color="#EBEBEB" />
                    <stop offset="1" stop-color="#E3E1E1" />
                </linearGradient>
                <linearGradient id="paint1_linear_7750_93934" x1="26.5695" y1="7" x2="26.5695" y2="52.9167" gradientUnits="userSpaceOnUse">
                    <stop stop-color="#F6F6F6" />
                    <stop offset="1" stop-color="#909090" />
                </linearGradient>
                <linearGradient id="paint2_linear_7750_93934" x1="56.3055" y1="0.868164" x2="56.3055" y2="24.2293" gradientUnits="userSpaceOnUse">
                    <stop stop-color="#F6F6F6" />
                    <stop offset="1" stop-color="#909090" />
                </linearGradient>
                <linearGradient id="paint3_linear_7750_93934" x1="56.375" y1="36.3125" x2="56.375" y2="74.9792" gradientUnits="userSpaceOnUse">
                    <stop stop-color="#F6F6F6" />
                    <stop offset="1" stop-color="#909090" />
                </linearGradient>
                <clipPath id="clip0_7750_93934">
                    <rect width="30.6111" height="25.7778" fill="white" transform="translate(41 0.0625)" />
                </clipPath>
            </defs>
        </svg>




    );
};

export default IconHoldUser;
