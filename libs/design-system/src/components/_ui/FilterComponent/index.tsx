import React from 'react';
import filterTypeToComponent from './config';
import { useTranslation } from '../../../i18n/client';

interface FilterComponentProps {
  filterValues: Record<string, Condition>;
  setFilterValues: (filterValues: Record<string, Condition>) => void;
  onRequest?: (
    value: Condition,
    setOptions: (options: unknown) => void
  ) => void;
  onSaveFilters?: () => void;
  onClearSavedFilters?: () => void;
  showFixedButtons?: boolean; // 控制是否显示底部固定按钮
}

export interface Condition {
  labelCh: string;
  labelEn: string;
  data?: string | Record<string, string>;
  rule?: string;
  value: string;
  filterType: string;
  active: boolean;
  checked: boolean;
  readOnly?: boolean;
  require?: boolean;
}

const FilterComponent: React.FC<FilterComponentProps> = ({
  filterValues,
  setFilterValues,
  onRequest,
  onSaveFilters,
  onClearSavedFilters,
  showFixedButtons = false, // 默认显示底部固定按钮
}) => {
  const { t } = useTranslation();
  const handleFilterChange = (filterValue: Condition) => {
    const newFilterValues = {
      ...filterValues,
      [filterValue.value]: filterValue,
    };
    setFilterValues(newFilterValues);
  };

  // Sort filters to ensure conversationStart and conversationEnd are in the correct order
  const sortedFilters = [...Object.values(filterValues)].sort((a, b) => {
    if (a.value === 'conversationStart') return -1;
    if (b.value === 'conversationStart') return 1;
    if (a.value === 'conversationEnd') return -1;
    if (b.value === 'conversationEnd') return 1;
    return 0;
  });

  return (
    <div className="flex flex-col gap-2 w-full relative">
      {/* Filter items with bottom padding to prevent overlap */}
      {/* <div className={showFixedButtons ? "pb-16" : ""}> */}
        {sortedFilters.map((filter, index) => {
          if (!filter.active) return null; // Skip inactive filters

          const FilterComponent = filterTypeToComponent[filter.filterType];
          if (!FilterComponent) return null; // Skip if no component is mapped

          const componentProps = onRequest
            ? {
              labelCh: filter.labelCh,
              labelEn: filter.labelEn,
              value: filterValues[filter.value],
              onChange: handleFilterChange,
              onRequest: (
                value: Condition,
                setOptions: (options: unknown) => void
              ) => {
                onRequest(value, setOptions);
              },
            }
            : {
              labelCh: filter.labelCh,
              labelEn: filter.labelEn,
              value: filterValues[filter.value],
              onChange: handleFilterChange,
            };

          return (
            <FilterComponent
              key={index}
              {...componentProps}
            />
          );
        })}
      {/* </div> */}

      <div className={showFixedButtons ? "mb-12" : ""}></div>

      {/* Gradient overlay - 只在显示按钮时添加 */}
      {showFixedButtons && (
        <div className="fixed bottom-0 left-0 right-0 h-16 bg-gradient-to-t from-white to-transparent pointer-events-none z-40" />
      )}

      {/* Fixed bottom buttons - aligned to the right */}
      {showFixedButtons && onSaveFilters && onClearSavedFilters && (
        <div className="fixed bottom-3 right-4 flex gap-2 z-50 whitespace-nowrap">
          <button
            onClick={onSaveFilters}
            className="px-4 py-3 bg-orange-400 hover:bg-orange-500 text-white font-medium rounded-lg shadow-lg transition-colors duration-200 text-sm"
          >
            {t('global.saveFilters')}
          </button>
          <button
            onClick={onClearSavedFilters}
            className="px-4 py-3 bg-orange-400 hover:bg-orange-500 text-white font-medium rounded-lg shadow-lg transition-colors duration-200 text-sm"
          >
            {t('global.clearSavedFilters')}
          </button>
        </div>
      )}
    </div>
  );
};

export default FilterComponent;
