// hooks/useMediaUrl.ts
import { useMemo } from 'react';
import { useRole } from "@cdss-modules/design-system";
import { microfrontends } from "../../../../../apps/ctint-mf-message/@types/microFrontendConfigs";
import { MediaItem } from "@cdss-modules/design-system/@types/Message";

interface MediaModeSetting {
  mode: 'direct' | 'proxy';
  proxyPath?: string;
}

export const useMediaUrl = () => {
  const { globalConfig } = useRole();
  const mediaModeSetting: MediaModeSetting = useMemo(() => {
    const microfrontendsConfig: microfrontends = globalConfig?.microfrontends;
    const mediaMode: undefined | 'direct' | 'proxy' =
      microfrontendsConfig['ctint-mf-message']['mediaMode'];
    const proxyPath: string | undefined =
      microfrontendsConfig['ctint-mf-cdss']['host'] + microfrontendsConfig['ctint-mf-message']['messageBasepath']+'/api/v1/cdss/'+ microfrontendsConfig['ctint-mf-message']['tenant'] +'/medias/';

    // 使用简洁的if语句来定义mediaModeSetting
    if (mediaMode === 'proxy') {
      return {
        mode: 'proxy',
        proxyPath,
      };
    } else {
      return {
        mode: 'direct',
      };
    }
  }, [globalConfig]);

  const transformMediaUrl = (media: MediaItem | undefined): MediaItem | undefined => {
    // 处理 undefined 情况
    if (!media) {
      return media; // 返回 undefined
    }

    if (mediaModeSetting.mode === 'proxy' && mediaModeSetting.proxyPath && media.id) {
      // 检查 media.url 的域名是否包含 "mypurecloud"
      if (media.url) {
        try {
          const url = new URL(media.url);
          // 如果域名包含 "mypurecloud"，不需要替换 url
          if (url.hostname.includes('mypurecloud')) {
            return media;
          }
        } catch (error) {
          // 如果 URL 解析失败，继续原有逻辑
          console.warn('Failed to parse media URL:', media.url, error);
        }
      }

      // 创建新对象而不是修改原对象
      return {
        ...media,
        url: `${mediaModeSetting.proxyPath}${media.id}`
      };
    }

    // 如果不需要修改，返回原对象
    return media;
  };

  return {
    mediaModeSetting,
    transformMediaUrl,
    isProxyMode: mediaModeSetting.mode === 'proxy'
  };
};
