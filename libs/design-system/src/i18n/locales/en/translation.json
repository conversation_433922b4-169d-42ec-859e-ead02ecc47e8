{"global": {"lng": "en", "home": "Home", "logout": "Logout", "search": "Search", "greeting": "Greeting", "greetingMessage": "Greeting Message", "english": "English", "chineseCantonese": "Chinese (Cantonese)", "inputContent": "Input content", "cancel": "Cancel", "confirm": "Confirm", "success": "Success", "error": "Error", "greetingUpdateSuccess": "Greeting message updated successfully.", "greetingUpdateFailed": "Failed to update greeting message.", "greetingUpdateError": "Error updating greeting message.", "saveFilters": "Save filters", "clearSavedFilters": "Clear saved filters"}, "pagination": {"jump": "Go to", "total": "Total {{count}} items", "perPage": "/ page"}, "notAvailable": "Not Available", "similarity": {"similarityScore": "<span class='text-grey'>Result with <span class='text-black font-bold'>{{score}}%</span> similarity was detected at: </span>", "othersResult": {"title": "Others Result {{index}} - detected at: {{timerStart}} - {{timerEnd}}", "similarityScore": "<span class='text-grey'>Result with <span class='text-black font-bold'>{{score}}%</span> similarity was detected"}}, "metaDataVerification": {"expect": "{{entityType}} should be to {{entityValue}}"}, "evaluation": {"evaluationForm": "Evaluation Form", "evaluator": "Evaluator", "releaseAt": "Release At", "passed": "Passed", "failed": "Failed", "toBeReviewed": "To be reviewed", "notEvaluated": "Not evaluated", "step": "Step", "matched": "Matched", "similarity": "Similarity", "metaDataVerification": "Meta Data Verification", "importantResponse": "Important Response", "notes": "Notes", "overrideTo": "Override To", "because": "Because...", "submit": "Submit", "leaveComment": "Leave a comment", "noComment": "No comment", "finalResult": "Final Result", "more": "More"}, "history": {"email": {"subject": "Subject", "from": "From", "to": "To", "cc": "Cc", "bcc": "Bcc", "draft": "Draft"}}}