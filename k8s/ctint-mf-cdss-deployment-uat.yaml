apiVersion: apps/v1
kind: Deployment
metadata:
  name: ctint-mf-cdss-deployment
  namespace: cdss-frontend
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ctint-mf-cdss
  template:
    metadata:
      labels:
        app: ctint-mf-cdss
    spec:
      containers:
      - name: ctint-mf-cdss
        image: cdss3uatacr.azurecr.io/ctint-mf-cdss:1
        ports:
          - name: http
            containerPort: 3000
            protocol: TCP
        resources:
          limits:
            cpu: 500m
            memory: 256Mi
          requests:
            cpu: 50m
            memory: 128Mi
        env:
        - name: CDSS_PUBLIC_ENVIRONMENT
          value: uat
        - name: GLOBAL_CONFIG_FILE
          value: ctint-global-config-uat.yaml
        volumeMounts:
        - name: ctint-global-config
          mountPath: /app/orign_dist/apps/ctint-mf-cdss/public/config
      volumes:  
      - name: ctint-global-config
        azureFile:
          secretName: ctintcdss3uatakssa-share
          shareName: ctint-cdss-globalconfig
          readOnly: false