apiVersion: apps/v1
kind: Deployment
metadata:
  name: ctint-mf-campaign-deployment
  namespace: cdss-frontend
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ctint-mf-campaign
  template:
    metadata:
      labels:
        app: ctint-mf-campaign
    spec:
      containers:
      - name: ctint-mf-campaign
        image: cdss3uatacr.azurecr.io/ctint-mf-campaign:1
        ports:
          - name: http
            containerPort: 3000
            protocol: TCP
        resources:
          limits:
            cpu: 100m
            memory: 128Mi
          requests:
            cpu: 10m
            memory: 50Mi
        env:
        - name: CDSS_PUBLIC_ENVIRONMENT
          value: uat
        - name: GLOBAL_CONFIG_FILE
          value: ctint-global-config-uat.yaml
        volumeMounts:
        - name: ctint-global-config
          mountPath: /app/orign_dist/apps/ctint-mf-campaign/public/config
      volumes:
      - name: ctint-global-config
        azureFile:
          secretName: ctintcdss3uatakssa-share
          shareName: ctint-cdss-globalconfig
          readOnly: false
