import { useInteractionContext } from '@cdss-modules/design-system/context/InteractionContext';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import { useEffect, useRef, useState } from 'react';
import { ChevronLeft } from 'lucide-react';
import QueuePerformance from '../queuePerformance';
import { Swiper, SwiperSlide } from 'swiper/react';
import type { Swiper as SwiperType } from 'swiper';
import { Loader, useRole } from '@cdss-modules/design-system';

import { Autoplay } from 'swiper/modules';

// Import Swiper styles
import 'swiper/css';
import {
  QueueMetrics,
  QueueStatistics,
  QueueStatisticsInitData,
} from '../queuePerformance/agenQueueData';
import {
  floorPlansInitData,
  agentStatistics,
  AgentBaseInfo,
  FloorPlan,
  AgentFullInfo,
  AgentStatistics,
} from '../agentFloorPlan/floorPlanData';

import AgentFloorPlan from '../agentFloorPlan';
import { useRouteHandler } from '@cdss-modules/design-system';
import {
  GetActivityStatisticsInitData,
  GetAgentStatisticsInitData,
  GetAgentStatusList,
  GetQueues,
  GetQueuesStatisticsInitData,
  GetTenantConfig,
} from '../../../lib/api/index';
import { useLocation, useSearchParams } from 'react-router-dom';
import {
  TAgentQueueItem,
  TAgentStatus,
  TQueueInitData,
} from '../../../types/index';
export type SupervisorDashboardProps = {
  id: number;
};
const SupervisorDashboard = ({ id }: SupervisorDashboardProps) => {
  const { userConfig } = useRole();
  const [searchParams] = useSearchParams();
  const [fullScreen, setFullScreen] = useState(false);
  const initDashboadOrder = ['QueuesDashboard', 'AgentsDashboard'];
  const { i18n } = useTranslation();
  const targetLng = i18n.language === 'en' ? 'zh-HK' : 'en';
  const { lastMessage } = useInteractionContext();
  const swiperRef = useRef<SwiperType>();
  const [globalSettingForsuperDashboard, setGlobalSetting] = useState({
    agentsNum: '8',
    queueNum: '8',
    swiperSpeed: '7',
    agentsRowNum: '4',
  });
  const [pageReady, setPageReady] = useState(false);
  const [agentStatus, setAgentStatus] = useState<TAgentStatus[]>([]);
  const [dashboadOrder, setDashboardOrder] = useState(initDashboadOrder);
  const [agentQueueItems, setAgentQueueItems] = useState<TAgentQueueItem[]>([]);
  const [agentItems, setAgentItems] = useState<FloorPlan[]>(floorPlansInitData);
  const [height, setHeight] = useState(window.innerHeight);
  const [queuesInitData, setQueuesInitData] = useState<TQueueInitData[]>([]);
  const [loading, setLoading] = useState(true);

  const [
    agentActivityAndStatisticsInitData,
    setAgentActivityAndStatisticsInitData,
  ] = useState<AgentStatistics[]>([]);
  const [randomIndex, setRandomIndex] = useState(0);
  const { basePath } = useRouteHandler();
  const queueEventPattern = /^queues\..*\.conversation\.aggregate\.changed$/;
  const agentPresencePattern = /^agent\.presence\.(.*?)\.changed$/;
  const agentStatisticsPattern =
    /^user\.(.*?)\.conversation\.aggregate\.changed$/;

  const initConfig = {
    session: 'adminSuperDashboad',
    data: {
      members: [],
      queueOrder: [],
      dashboardOrder: [],
    },
  };

  const [tenantConfig, setTenantConfig] = useState<any>(initConfig);
  useEffect(() => {
    console.log('userconfig:', userConfig?.id);
    if (searchParams.get('fullscreen') === 'true') {
      setFullScreen(true);
    }
    // GET SupervisorDashboard configuration
    fetchTenantConfig();
    getAgentStatusList();
    // getQueuesStatisticsInitData();
    getInitData();
    //getActivityStatisticsInitData();
    // getAgentStatisticsInitData();

    // const timeout = setTimeout(() => {}, 1000);
    //fetchAgentQueues();

    // console.log(height);

    return () => {
      console.log('Component will unmount');
    };
  }, []);

  useEffect(() => {
    if (lastMessage?.data == 'ping' || lastMessage == null) {
      return;
    }
    // console.log(lastMessage);
    try {
      // console.log('lastMessage', JSON.parse(lastMessage.data));
      const lastMessageEvent = JSON.parse(lastMessage.data);
      if (queueEventPattern.test(lastMessageEvent.event)) {
        const eventQueueId = lastMessageEvent.eventData.data.queueId;
        const eventQueueData = lastMessageEvent.eventData.data.call;
        // console.log('eventQueueData', eventQueueData);
        const agentQueueItem: any = agentQueueItems.find(
          (item) => item.id === eventQueueId
        );
        if (!agentQueueItem) {
          return;
        }
        console.log('eventQueueData:', eventQueueData);
        const updateQueueItem = {
          ...agentQueueItem,
          data: {
            avgAnsSpd: eventQueueData.avgAnswered,
            avgHdlSpd: eventQueueData.avgHandle,
            callAbdRate: eventQueueData.abandonRate,
            callAnsRate: eventQueueData.answeredRate,
            callsDone: eventQueueData.nTalkComplete,
            callsOffered: eventQueueData.nOffered,
            longestWT: eventQueueData.maxWait,
            priority: '30',
            svl: eventQueueData.oServiceLevel,
            waitingCalls: eventQueueData.nWait,
          },
        };
        // console.log(updateQueueItem);
        const updatedQueueItems: any = agentQueueItems.map((item) =>
          item.id === eventQueueId ? updateQueueItem : item
        );
        setAgentQueueItems(updatedQueueItems);
      }
      // if (agentPresencePattern.test(lastMessageEvent.event)) {
      //   const match = lastMessageEvent.event.match(agentPresencePattern);
      //   const agentId = match[1];
      //   const agent = findAgentById(agentItems, agentId);
      //   if (agent) {
      //     const newValues = {
      //       type: lastMessageEvent.eventData.data.name,
      //     };
      //     const updateAgentItems = updateAgentStatus(
      //       agentItems,
      //       agentId,
      //       newValues
      //     );
      //     setAgentItems(updateAgentItems);
      //     //console.log(updateAgentItems);
      //   }
      // }
      if ('agent.state.changed' == lastMessageEvent.event) {
        const agentId = lastMessageEvent.eventData.data.id;
        const agent = findAgentById(agentItems, agentId);
        const agentStatusId =
          lastMessageEvent.eventData.data.presence.presenceDefinition.id;
        const agentStat = agentStatus.find(
          (status) => status.id == agentStatusId
        );
        if (agent) {
          console.log(
            'lastMessageEvent type',
            lastMessageEvent.eventData.data.presence.presenceDefinition
              .systemPresence
          );
          const newValues = {
            type: lastMessageEvent.eventData.data.presence.presenceDefinition
              .systemPresence,
            activity: lastMessageEvent.eventData.data.routingStatus.status,
            subType: agentStat?.languageLabelEnUs,
          };
          const updateAgentItems = updateAgentStatus(
            agentItems,
            agentId,
            newValues
          );
          setAgentItems(updateAgentItems);
        }
      }
      if (agentStatisticsPattern.test(lastMessageEvent.event)) {
        const match = lastMessageEvent.event.match(agentStatisticsPattern);
        const agentId = match[1];
        // console.log(agentId);
        const agent = findAgentById(agentItems, agentId);
        // console.log(agent);
        if (agent) {
          // console.log(
          //   lastMessageEvent.eventData.data.activity.outboundCalls,
          //   lastMessageEvent.eventData.data.activity.inboundCalls
          // );
          const newValues = {
            callsMade: lastMessageEvent.eventData.data.activity.outboundCalls,
            callsReceived:
              lastMessageEvent.eventData.data.activity.inboundCalls,
          };
          const updateAgentItems = updateAgentStatus(
            agentItems,
            agentId,
            newValues
          );
          setAgentItems(updateAgentItems);
        }
      }
    } catch (error) {
      console.error('Invalid message format:', lastMessage, error);
      return;
    }

    // TODO: add logic to handle received message
  }, [lastMessage]);

  useEffect(() => {
    // console.log(dashboardItems);
  }, [swiperRef]);

  useEffect(() => {
    if (tenantConfig.data.globalSetting) {
      // console.log(tenantConfig.data.globalSetting);
      setGlobalSetting(tenantConfig.data.globalSetting);
      // console.log(
      //   'globalSettingForsuperDashboard',
      //   globalSettingForsuperDashboard
      // );
    }

    const agentsBase: AgentBaseInfo[] = tenantConfig.data.members;
    // const agentFullInfo: AgentFullInfo[] = agentsBase.map((agent) => ({
    //   ...agent,
    //   ...agentStatistics,
    // }));

    const agentFullInfo1: AgentFullInfo[] = agentsBase.map((agent) => {
      const agentId = agent.id;
      // 使用 find 来查找匹配的数据
      const matchedActivity = agentActivityAndStatisticsInitData.find(
        (agentActivityAndStatistics) =>
          agentActivityAndStatistics.agentId === agentId
      );
      console.log('real agent list', { ...agent, ...matchedActivity });
      // 如果找到匹配的数据就合并，否则只返回 agent 数据
      return { ...agent, ...matchedActivity } as AgentFullInfo;
    });

    const agentFullInfo: AgentFullInfo[] =
      agentActivityAndStatisticsInitData.map((agentActivityAndStatistics) => {
        const agentId = agentActivityAndStatistics.agentId;
        // 使用 find 来查找匹配的数据
        const matchedAgent = agentsBase.find((agent) => agent.id === agentId);
        console.log('real agent list', {
          ...matchedAgent,
          ...agentActivityAndStatistics,
        });
        // 如果找到匹配的数据就合并，否则只返回 agentActivityAndStatistics 数据
        return {
          ...matchedAgent,
          ...agentActivityAndStatistics,
        } as AgentFullInfo;
      });

    // console.log(agentFullInfo, agentFullInfo);
    const floorPlansData = distributeAgentsToFloors(agentFullInfo);
    // console.log('floorPlansData', floorPlansData);
    setAgentItems(floorPlansData);

    const dashboardOrder = tenantConfig.data.dashboardOrder.map(
      ({ name }: { name: string }) => name
    );
    if (dashboardOrder.length != 0) {
      setDashboardOrder(dashboardOrder);
    }

    fetchAgentQueues();
    // console.log('agentStatus', agentStatus);
    // console.log(tenantConfig);
  }, [agentActivityAndStatisticsInitData, globalSettingForsuperDashboard]);

  useEffect(() => {
    // const timeout = setTimeout(updateDataBySeconds, 2000); // 每5秒更新一次
    // return () => clearTimeout(timeout);
    if (agentQueueItems.length > 0) {
      setPageReady(true);
    }
    // console.log('agentQueueItems', agentQueueItems);
  }, [agentQueueItems]);

  const fetchTenantConfig = async () => {
    const result = await GetTenantConfig(basePath)
      .catch(async (e) => {
        // console.error(e);
        console.error(e.response.data);
        if (e.response.data.data === null || !e.response.data.data) {
          alert('NO ANY Configuration,Please Go To Setup');
        }
        console.log('no config init');
      })
      .finally(() => {});

    if (result) {
      //console.log('fetch tenant config:', result.data.data);
      const config = result.data.data;
      setTenantConfig({ ...initConfig, data: config });
    }
  };

  const fetchAgentQueues = async () => {
    if (queuesInitData.length == 0) {
      console.log('no queues init yet');
      return;
    }
    const GetQueuesResp = await GetQueues(basePath, 'user', userConfig?.id);
    // const queuesStaticticsInitResp =
    //   await GetQueuesStatisticsInitData(basePath);
    // const queuesStaticticsInitData = queuesStaticticsInitResp.data.data;
    // console.log('queuesStaticticsInitData', queuesInitData);
    const agentQueueData: TAgentQueueItem[] = GetQueuesResp.data.data;
    const agentQueueDataAfterCombined = agentQueueData.map((item) => {
      const matchingQueueInitData = queuesInitData.find(
        (dataItem) => dataItem.queueId === item.id
      );
      if (matchingQueueInitData) {
        // console.log('matchingData', matchingQueueInitData);
        const singleQueueStaticticsInitData: QueueStatistics = {
          svl: matchingQueueInitData.call.oServiceLevel + '',
          callsDone: matchingQueueInitData.call.nTalkComplete,
          avgAnsSpd: matchingQueueInitData.call.avgAnswered + '',
          callAnsRate: matchingQueueInitData.call.answeredRate,
          priority: '',
          callsOffered: matchingQueueInitData.call.nOffered,
          avgHdlSpd: matchingQueueInitData.call.avgHandle + '',
          callAbdRate: matchingQueueInitData.call.abandonRate,
          waitingCalls: matchingQueueInitData.call.nWait,
          longestWT: matchingQueueInitData.call.maxWait,
          // longestWT: '12387',
        };
        return {
          ...item,
          data: singleQueueStaticticsInitData,
        };
      }
    });
    // console.log('agentQueueDataAfterCombined', agentQueueDataAfterCombined);
    // console.log(
    //   'orderingAgentQueues',
    //   orderingAgentQueues(
    //     agentQueueDataAfterCombined,
    //     tenantConfig.data.queueOrder
    //   )
    // );
    setAgentQueueItems(
      orderingAgentQueues(
        agentQueueDataAfterCombined,
        tenantConfig.data.queueOrder
      )
    );
  };

  function distributeAgentsToFloors(agents: AgentFullInfo[]): FloorPlan[] {
    // console.log(
    //   'globalSetting.agentsRowNum',
    //   globalSettingForsuperDashboard.agentsNum,
    //   globalSettingForsuperDashboard.agentsRowNum
    // );
    const AGENTS_PER_FLOOR =
      Number(globalSettingForsuperDashboard.agentsNum) *
      Number(globalSettingForsuperDashboard.agentsRowNum); // 每层最大容纳的 agent 数量
    const floors: FloorPlan[] = [];
    // console.log('AGENTS_PER_FLOOR', AGENTS_PER_FLOOR);

    const activeAgents = agents.filter((agent) => agent.isActive);

    // 计算需要多少层
    const numberOfFloors = Math.ceil(activeAgents.length / AGENTS_PER_FLOOR);
    // console.log(activeAgents.length);
    for (let i = 0; i < numberOfFloors; i++) {
      // 为每层创建新的 floor 对象
      const floor: FloorPlan = {
        id: `floor-${i + 1}`,
        name: `Office Floor ${i + 1}`,
        rows: 5,
        cols: 7,
        agents: activeAgents.slice(
          i * AGENTS_PER_FLOOR,
          (i + 1) * AGENTS_PER_FLOOR
        ),
      };

      floors.push(floor);
    }

    return floors;
  }

  const orderingAgentQueues = (
    agentQueueData: any[],
    queueOrderConfig: any
  ) => {
    // console.log(queueOrderConfig);
    if (queueOrderConfig.length == 0) {
      //setPageReady(true);
      return queueOrderConfig;
    }
    // 创建一个 Set 存储 queueOrderConfig 中所有的 id，用于快速查找哪些 id 是需要的
    const configIds = new Set(queueOrderConfig.map((item: any) => item.id));

    // 先过滤出在 queueOrderConfig 中存在的数据
    const filteredData = agentQueueData.filter((item) => {
      if (item) {
        return configIds.has(item.id);
      }
    });

    // 按照 queueOrderConfig 的顺序排序
    return queueOrderConfig
      .map((configItem: any) =>
        filteredData.find((item) => item.id === configItem.id)
      )
      .filter(Boolean); // 移除可能的 undefined 项
  };
  // const updateDataBySeconds = () => {
  //   // const data = dashboardItems;
  //   const [newData, actIndex] = randomUpdateItem(
  //     agentQueueItems,
  //     Math.floor(
  //       Math.random() * Math.floor(Math.random() * agentQueueItems.length)
  //     )
  //   );
  //   setRandomIndex(actIndex as number);
  //   setAgentQueueItems(newData as any);
  // };

  const getQueuePageItems = () => {
    const settingPerPage = Number(globalSettingForsuperDashboard.queueNum)
      ? Number(globalSettingForsuperDashboard.queueNum)
      : 8;
    // console.log(settingPerPage);
    const itemsPerPage =
      height < 1075
        ? settingPerPage > 6
          ? 6
          : settingPerPage
        : settingPerPage;
    const totalPages = Math.ceil(agentQueueItems.length / itemsPerPage);
    // console.log(totalPages);
    return [...Array(totalPages)].map((_, index) => {
      const startIndex = index * itemsPerPage;
      const endIndex = startIndex + itemsPerPage;
      return agentQueueItems.slice(startIndex, endIndex);
    });
  };

  const getAgentStatusList = async () => {
    const agentStatusRS = await GetAgentStatusList(basePath);
    // console.log(agentStatusRS.data.data);
    setAgentStatus(agentStatusRS.data.data);
  };

  const enableFullScreen = () => {
    setFullScreen(true);
  };

  const findAgentById = (data: any, targetAgentId: string) => {
    // 遍历所有楼层
    for (const floor of data) {
      // 在当前楼层的 agents 数组中查找对应的 agent
      const agent = floor.agents.find(
        (agent: any) => agent.id === targetAgentId
      );
      if (agent) {
        return agent;
      }
    }
    return null; // 如果没找到返回 null
  };

  const updateAgentStatus = (data: any, agentId: string, newValues: any) => {
    // 创建数据的深拷贝
    const newData = JSON.parse(JSON.stringify(data));

    // 遍历所有楼层
    for (const floor of newData) {
      const agentIndex = floor.agents.findIndex(
        (agent: any) => agent.id === agentId
      );
      if (agentIndex !== -1) {
        // 更新找到的 agent
        floor.agents[agentIndex] = {
          ...floor.agents[agentIndex],
          ...newValues,
        };
        return newData; // 返回更新后的数据
      }
    }
    return data; // 如果没找到，返回原数据
  };

  const getQueuesStatisticsInitData = async () => {
    const queuesStatisticsInitDataRs =
      await GetQueuesStatisticsInitData(basePath);

    return queuesStatisticsInitDataRs.data.data;
    // console.log(queuesStatisticsInitDataRs);
  };

  const getActivityStatisticsInitData = async () => {
    const activityStatisticsInitData =
      await GetActivityStatisticsInitData(basePath);
    return activityStatisticsInitData.data.data;
    // setAgentActivityInitData(activityStatisticsInitData.data.data);
  };

  const getAgentStatisticsInitData = async () => {
    const agentStatisticsInitData = await GetAgentStatisticsInitData(basePath);
    return agentStatisticsInitData.data.data;
    // console.log(agentStatisticsInitData);
  };

  const getInitData = async () => {
    const [
      agentActivityInitData,
      agentStatisticsInitData,
      queuesStatisticsInitData,
    ] = await Promise.all([
      getActivityStatisticsInitData(),
      getAgentStatisticsInitData(),
      getQueuesStatisticsInitData(),
    ]);
    // console.log(agentActivityInitData, agentStatisticsInitData);
    const agentActivityAndStatisticsInitData: AgentStatistics[] = [];
    agentActivityInitData.map((activity: any, index: number) => {
      const activityAgentId = activity.id;
      agentStatisticsInitData.map((agentStatistics: any) => {
        const agentStatisticsAgentId = agentStatistics.userId;
        if (activityAgentId === agentStatisticsAgentId) {
          const agentInitData: AgentStatistics = {
            agentId: activityAgentId,
            callsReceived: agentStatistics.activity.inboundCalls,
            callsMade: agentStatistics.activity.outboundCalls,
            type: activity.presence.presenceDefinition.systemPresence,
            subType: activity.presence.presenceDefinition.systemPresence,
            activity: activity.routingStatus.status,
            duration: timeStringToMs(activity.presence.modifiedDate),
            label: '',
            color: '',
          };
          // console.log(activityAgentId, agentInitData);
          agentActivityAndStatisticsInitData.push(agentInitData);
        }
      });
      // console.log(activityAgentId, agentStatisticsAgentId);
    });
    setQueuesInitData(queuesStatisticsInitData); // queue init data
    setAgentActivityAndStatisticsInitData(agentActivityAndStatisticsInitData);
    setLoading(false);
  };

  const timeStringToMs = (time: string): number => {
    const givenTime = new Date(time).getTime();
    const currentTime = new Date().getTime();
    const timeDiff = currentTime - givenTime;

    return timeDiff;
  };

  return (
    <div
      className={`flex flex-col ${
        fullScreen ? 'w-full h-full left-0 top-0 z-[99999] fixed' : ''
      } bg-white`}
    >
      {loading && (
        <div
          style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            zIndex: 999,
          }}
        >
          <Loader size={100} />
        </div>
      )}
      {!fullScreen && (
        <div className="">
          <div className="mx-auto flex items-center justify-between p-1 ">
            <div className="flex items-center gap-3">
              <button className="p-2 hover:bg-gray-100 rounded">
                <ChevronLeft className="h-5 w-5 text-gray-600" />
              </button>
              <span className="font-medium text-2xl">Supervisor Dashboard</span>
            </div>

            <div className="flex items-center gap-2 pr-2">
              <button
                className="px-2 py-1 bg-black text-white rounded hover:bg-gray-800 w-20"
                onClick={() => {
                  // swiperRef.current?.update();
                  swiperRef.current?.slideNext();
                  //swiperRef.current?.slidePrev()
                }}
                onMouseEnter={() => {
                  swiperRef.current?.autoplay.stop();
                }}
                onMouseLeave={() => {
                  swiperRef.current?.autoplay.start();
                }}
              >
                Next
              </button>
              <button
                onClick={enableFullScreen}
                className="p-2 hover:bg-gray-100 rounded"
                style={{
                  backgroundImage: `url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAKRSURBVHgB1ZrtdZswFIYf5/R/vUGVCdpOUG9QbxA2SDcwI7QTJNnAncBkAtsTgCcwncDVNZAQjkACBJj3nNccQLq6X/rEC/xgqbnW/KqpNL/lz5alMknOg+ZRM8rvJ4Mot9HcaV46cq8ZkBk9uuJnuitu4hMjGDKE4lVuGACKLNyXkRjjMRoPDO91E6XNNT2xgdEV95ZSt6B8ZyPWwNRKV/mAIxTT5LyNZwwde2EwIKZ+BJBZ9FUzxWz4D+yjh9R9wSxD5piflva/04AN9R6IsUNh96RykNM0ZIddG/+NG2L6OYG8raZUeltj3ZUqbSxCU+xQNHvY9t6lLVH+V3HzqSQ4oB0Cspwvw2Xi2ZHlcllJ6VfPuOORLEppYcCK9ljRYmgrQWGOwjPuKKIQFin0yPxwjb4YoMg2IHPDSnN5h3v6LCv3nxkOXxzLXftc05BVNxPKNXasN6Ts69C+a9mYT8X7yt4u8Lx5GBmJGHBhvkjnbsCHpcQsIQa4rHFuFnM3IBEDDu3qDHocmNAOVwNOroU170v0Gbmko+xjmwhEvHtIrn/xh2NF9otjvYMYsHUs7BqpLjhX7l0jEBWdOGJ+kMxJinnglfnhj/wUOzJZ1cmmZtlCQMTHWVzqyr5CWepJxKtp28WBUfVBSPPKL8QOhZ9jldAi48lUSTzYdCJ3K8cqjU4IGyrtsUPhJwJNTgjLBU1Hi6Jo3R45Icvff4Z3ssUMsPejhCx/TzUyVpb277FAMaPD3ToEwNQKV9n6a00ITK20Me/nZkRIT0jopvrIF+AJimGPU6rcM9BJSQiDKn7GQ8rYoMhOkodQvM1arDcUWY72+YK/o6fiC/xA8T6DFn+5UaX3aU5Zw5/y6xYP29L/z/QvNnJeVPcAAAAASUVORK5CYII=')`,
                  backgroundSize: 'cover',
                  backgroundPosition: 'center',
                  backgroundRepeat: 'no-repeat',
                  width: '1.5rem',
                  height: '1.5rem',
                }}
              >
                {/* <Settings className="h-5 w-5 text-gray-600" /> */}
              </button>
            </div>
          </div>
        </div>
      )}
      {pageReady && (
        <Swiper
          className="mySwiper w-full"
          modules={[Autoplay]}
          autoplay={{
            delay: Number(globalSettingForsuperDashboard.swiperSpeed) * 1000,
            disableOnInteraction: false,
            pauseOnMouseEnter: true,
          }}
          loop={true}
          slidesPerView="auto"
          observer={true}
          observeParents={true}
          onSwiper={(swiper) => {
            swiperRef.current = swiper;
          }}
        >
          {dashboadOrder.map((type) => {
            // const DashboardComponent = dashboardNameMapping[type];
            if (type === 'QueuesDashboard') {
              return getQueuePageItems().map((pageItems, index) => (
                <SwiperSlide key={index}>
                  <QueuePerformance
                    page={index + 1}
                    dashboardItems={pageItems}
                    actIndex={randomIndex}
                  />
                </SwiperSlide>
              ));
            }
            if (type === 'AgentsDashboard') {
              return agentItems.map(
                (floorPlan, floorIndex) =>
                  floorPlan.agents?.length > 0 && (
                    <SwiperSlide key={`floor-${floorIndex}`}>
                      <AgentFloorPlan
                        page={floorIndex + 1}
                        floorPlan={floorPlan}
                        rowSize={Number(
                          globalSettingForsuperDashboard.agentsNum
                        )}
                      />
                    </SwiperSlide>
                  )
              );
            }
          })}
        </Swiper>
      )}
      {fullScreen && (
        <div className="flex items-center justify-end gap-2 pr-2 w-full right-5 bottom-5 z-[99999] absolute">
          <button
            className="px-2 py-1 bg-black text-white rounded hover:bg-gray-800 w-20"
            onClick={() => {
              swiperRef.current?.slideNext();
              //swiperRef.current?.slidePrev()
            }}
            onMouseEnter={() => {
              swiperRef.current?.autoplay.stop();
            }}
            onMouseLeave={() => {
              swiperRef.current?.autoplay.start();
            }}
          >
            Next
          </button>
          <button
            onClick={() => {
              setFullScreen(false);
            }}
            className="p-2 hover:bg-gray-100 rounded"
            style={{
              backgroundImage: `url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAJaSURBVHgB1ZqLcYMwEETXqYAS1EFcgt0BHUAnqAO7A6eEdEA6gFQAqQB3QHSGyygeYbCEJLMzO+TDOG99J0US3mEdJcqp8ruyUN6PP0u0e9rRtfK38tf4fTQRXKFcKveWrpRzDKGDg3ewBzf5ggBBfIDfu4AHCQzl7gO5wYrVyOD/XTeZ/mYKRxVAcPDVWuoV4K1DpABiQ987w0IJxOn5OXdYOLCbWJBCiL4sy9t14p5qDr6IAc7wTdP0JArx4F45BS9eAZ6uDyrArZSYAlw2AD9ZBeET8nA49GmargVvrELuCz5Jkp6VZdka8MYqeFvnEJguCrECPLlkeOEL3hSA1HWdKzz71kZ56AAcwhGenL9h2P4FlxobUO0ER93YS0SoAKsoCpfX/6QAjc8A+iw0JdMUu9DE7g+eLaX8G7j3qqrKZSx0u/GLzeoNGxcFuMJCNIuo8kN1gdGn0wmhRAPh6f6jvp3qa9Z+v/c9vhqqQA0LtW2L4/GI63W6gFQlz2opwA8sVdf1bAjP+rauAIsqQY6kGzvV2aoH6Z8UzeO8tuEVJov2AbavvdCCk5Su8DRg9WVygAD/NvjSFR7a7MQhPAfI9QDURovOgh7B6yEc1jdPt8/iKiyBD+QLDHpYhReCN777s1WgdfuLwEvMaHKDfz6fY8M3WCCBjR/uknIAsYHv/fTTGgkgNjRbwlISQI+NwrOodLEe8uVYSQJhH3xU8PTQWwJewTus0DJzEsofgBdw71s4XQJDj7qcbJdwBN9hHQnlA4azSv7IjdB+fx1NO6if8foJyxMRXb/o4Bp6DPwS0AAAAABJRU5ErkJggg==')`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              backgroundRepeat: 'no-repeat',
              width: '1.5rem',
              height: '1.5rem',
            }}
          >
            {/* <Settings className="h-5 w-5 text-gray-600" /> */}
          </button>
        </div>
      )}
    </div>
  );
};

export default SupervisorDashboard;
