import { Phone } from 'lucide-react';
import { FC, memo, useEffect, useRef, useState } from 'react';
import StatRow from './stateRow';
import Time from '../agentFloorPlan/time';
import { QueueStatistics } from './agenQueueData';

/** 
 * 
 * 
 * .highlight {
  animation: highlight 2.8s cubic-bezier(0.4, 0, 0.2, 1) infinite;
  position: relative;
}
*/
interface QueueStateCardProps {
  title: string;
  data: QueueStatistics;
  index: number;
  actIndex: number;
}

const QueueStateCard: FC<QueueStateCardProps> = memo(
  ({ title, data, index, actIndex }) => {
    const waitingCallsRef = useRef(data.waitingCalls);
    const [waitingCallAnimation, setWaitingCallAnimation] = useState(false);
    console.log('QueueStateCard:', data);

    useEffect(() => {
      // 先进行比较
      // if (Number(waitingCallsRef.current) !== Number(data.waitingCalls)) {
      // console.log(
      //   '变化了:',
      //   waitingCallsRef.current,
      //   '->',
      //   data.waitingCalls
      // );
      //   setWaitingCallAnimation(true);
      // }
      // 比较完后才更新 ref

      if (Number(waitingCallsRef.current) > 0) {
        setWaitingCallAnimation(true);
      } else {
        setWaitingCallAnimation(false);
      }

      waitingCallsRef.current = data.waitingCalls;
    }, [data.waitingCalls]);

    const getColorByIndex = (index: number) => {
      const position = index % 4; // 获取在4个为一组的位置
      return position === 0 || position === 3 ? 'black' : 'orange';
    };

    const formatDuration = (ms: number, padZero = true): string => {
      if (ms === 0) return padZero ? '0s' : '0s';

      const seconds = Math.floor((ms / 1000) % 60);
      const minutes = Math.floor((ms / (1000 * 60)) % 60);
      const hours = Math.floor((ms / (1000 * 60 * 60)) % 24);
      const days = Math.floor(ms / (1000 * 60 * 60 * 24));

      const parts: string[] = [];

      if (days > 0) {
        parts.push(`${padZero ? days.toString().padStart(2, '') : days}d`);
      }

      if (hours > 0 || (days > 0 && (minutes > 0 || seconds > 0))) {
        parts.push(`${padZero ? hours.toString().padStart(2, '') : hours}h`);
      }

      if (minutes > 0 || (hours > 0 && seconds > 0)) {
        parts.push(
          `${padZero ? minutes.toString().padStart(2, '') : minutes}m`
        );
      }

      if (seconds > 0 || parts.length === 0) {
        parts.push(
          `${padZero ? seconds.toString().padStart(2, '') : seconds}s`
        );
      }

      return parts.join(' ');
    };

    // console.log(
    //   `QueueStateCard ${actIndex} and title is ${title} , rendering, data:`,
    //   data
    // );
    return (
      <div className="flex flex-col h-full">
        <div
          className={`${getColorByIndex(index) === 'black' ? 'bg-black' : 'bg-orange-400'} text-white px-3 py-2 flex items-center gap-2`}
        >
          <Phone className="h-4 w-4" />
          <span className="font-semibold text-2xl">{title}</span>
        </div>

        <div className="grid grid-cols-3 flex-1">
          <div className="flex flex-col h-full">
            <StatRow
              label="SVL(%)"
              value={Number(data.svl).toFixed(0) + '%'}
              isGray={true}
            />
            <StatRow
              label="Calls Done"
              value={data.callsDone}
            />
            <StatRow
              label="Avg.Ans.Spd"
              value={formatDuration(Number(data.avgAnsSpd))}
              isGray={true}
            />
            <StatRow
              label="Call Ans.Rate"
              value={data.callAnsRate}
            />
          </div>

          <div className="flex flex-col h-full">
            <StatRow
              label="Calls Offered"
              value={data.callsOffered}
            />
            <StatRow
              label="Avg.HDL.Spd"
              value={formatDuration(Number(data.avgHdlSpd))}
              isGray={true}
            />
            <StatRow
              label="Call Abd.Rate"
              value={data.callAbdRate}
            />
            <StatRow
              label=""
              value={''}
              isGray={true}
            />
          </div>

          <div
            className={`border h-full flex flex-col ${getColorByIndex(index) === 'black' ? 'border-black' : 'border-orange-300'}`}
          >
            <div className="px-3 py-2 flex-1 flex flex-col justify-center text-center">
              <div
                className="mb-4"
                style={{
                  animation: waitingCallAnimation
                    ? 'highlight 2.8s cubic-bezier(0.4, 0, 0.2, 1) infinite'
                    : '',
                  position: waitingCallAnimation ? 'relative' : undefined,
                }}
              >
                <div className={`text-2xl text-gray-600 font`}>
                  Waiting Calls
                </div>
                <div className={`text-red-500 font-bold text-2xl`}>
                  {data.waitingCalls}
                </div>
              </div>
              <div>
                <div className="text-2xl text-gray-600">Longest WT</div>
                <div className="text-red-500 font-bold text-2xl">
                  {/* {data.longestWT} */}
                  <Time
                    status={''}
                    isWaitingCall={true}
                    waitingCall={Number(data.waitingCalls)}
                    // waitingCall={Number(data.waitingCalls)}
                    initialTime={Number(data.longestWT)}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  },
  (prevProps, nextProps) => {
    // 只有当 title 或 data 发生变化时才重新渲染
    const shouldUpdate =
      prevProps.title === nextProps.title &&
      JSON.stringify(prevProps.data) === JSON.stringify(nextProps.data);
    if (!shouldUpdate) {
      //console.log(`QueueStateCard ${prevProps.data.avgAnsSpd} vs ${nextProps.data.avgAnsSpd}:`);
    }
    return shouldUpdate;
  }
);
QueueStateCard.displayName = 'QueueStateCard'; // 添加这行

export default QueueStateCard;
