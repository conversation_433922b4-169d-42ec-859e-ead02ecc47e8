interface TStateRowProps {
  label: string;
  value: number | string;
  isGray?: boolean;
}
const StatRow = ({ label, value, isGray = false }: TStateRowProps) => (
  <div
    className={`flex h-full items-center justify-between px-2 py-1.5 ${isGray ? 'bg-gray-100' : 'bg-white'}`}
  >
    <div className="text-xl text-gray-600 flex-1 text-">{label}</div>
    <div
      className="text-black font-semibold text-xl"
      style={{ width: '40%' }}
    >
      {value}
    </div>
  </div>
);

export default StatRow;
