@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body{
    @apply bg-common-bg;
  }

  /* width */
  ::-webkit-scrollbar {
    @apply w-2 h-2;
  }

  /* Track */
  ::-webkit-scrollbar-track {
    @apply bg-common-divider rounded-full;
  }

  /* Handle */
  ::-webkit-scrollbar-thumb {
    @apply bg-primary rounded-full;
    background-clip: padding-box;
  }

  /* Handle on hover */
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-black;
  }
}

@layer components {

  @keyframes spinner-animation {
    0% {
      stroke-dasharray: 1 98;
      stroke-dashoffset: -105;
    }
    50% {
      stroke-dasharray: 80 10;
      stroke-dashoffset: -160;
    }
    100% {
      stroke-dasharray: 1 98;
      stroke-dashoffset: -300;
    }
  }
  .spinner-animation {
    transform-origin: center;
    animation-name: animation;
    animation: spinner-animation 1.2s infinite;
  }
}

.swiper {
  width: 100%;
  height: 100%;
}

.swiper-slide {
  text-align: center;
  font-size: 18px;
  background: #fff;

  /* Center slide text vertically */
  display: flex;
  justify-content: center;
  align-items: center;
}
/* 
@keyframes highlight {
  0% {
    background-color: transparent;
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(233, 120, 20, 0);
  }
  20% {
    background-color: rgba(233, 120, 20, 0.8);
    transform: scale(1.015);
    box-shadow: 0 0 10px 3px rgba(233, 120, 20, 0.3);
  }
  35% {
    background-color: transparent;
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(233, 120, 20, 0);
  }
  65% {
    background-color: rgba(233, 120, 20, 0.8);
    transform: scale(1.015);
    box-shadow: 0 0 10px 3px rgba(233, 120, 20, 0.3);
  }
  80% {
    background-color: transparent;
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(233, 120, 20, 0);
  }
  100% {
    background-color: transparent;
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(233, 120, 20, 0);
  }
} */

/* .highlight {
  animation: highlight 2.8s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
} */


