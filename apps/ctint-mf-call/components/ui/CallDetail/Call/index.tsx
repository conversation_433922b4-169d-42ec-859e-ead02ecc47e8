'use client';

import { useEffect, useMemo, useState } from 'react';
import { useSearchParams, useLocation } from 'react-router-dom';
import {
    Panel,
    toast,
    Tooltip,
    useRouteHandler,
} from '@cdss-modules/design-system';
import CallPanel from './CallPanel';
import {
    cn,
    formatInteractionDuration,
} from '@cdss-modules/design-system/lib/utils';
import {
    TWrapUpFormState,
    TWrapUpOption,
} from '@cdss-modules/design-system/@types/Interaction';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import IconWrapup2 from '@cdss-modules/design-system/components/_ui/Icon/IconWrapUp2';
import IconTransferLine from '@cdss-modules/design-system/components/_ui/Icon/IconTransferLine';
import WrapUp from '@cdss-modules/design-system/components/_ui/WrapUp';
import IconEmptyRecords from '@cdss-modules/design-system/components/_ui/Icon/IconEmptyRecords';
import { TSubmitWrapup } from 'apps/ctint-mf-call/types';
import {
    fireSubmitWrapupCode,
    fireUpdateWrapupCode,
} from 'apps/ctint-mf-call/lib/api';
import { useTbarContext } from '@cdss-modules/design-system/context/TBarContext';
import { useHandleInteractionData } from '@cdss-modules/design-system/lib/hooks/useHandleInteractionData';
import React from 'react';
import { useGetCallDetail } from '@cdss-modules/design-system/lib/hooks/useGetCallDetail';
import Directory from '@cdss-modules/design-system/components/_ui/Directory';
interface CallProps {
    selectInteraction: (data: any) => void;
}
export const Call: React.FC<CallProps> = React.memo(
    ({ selectInteraction }) => {
        const initialState: TWrapUpFormState = {
            selectedItems: [],
            notes: '',
            expandedItems: [],
            itemRemarks: [],
            activeRemarkItems: [],
        };
        const [searchParams] = useSearchParams();
        const location = useLocation();
        const [openDirectory, setOpenDirectory] = useState(false);
        const [wrapUpError, setWrapUpError] = useState<string | null>(null);
        const [time, setTime] = useState<string>('00:00');
        const [cId, setConversationId] = useState<string | null>('');
        const [wrapUpFormState, setWrapUpFormState] =
            useState<TWrapUpFormState | null>(null);
        const [wrapUpParticipantIds, setWrapUpParticipantIds] = useState<string[]>(
            []
        );

        const { basePath } = useRouteHandler();
        const {
            wrapupContext: {
                selectLastActiveAgentId,
                selectLastActiveConvId,
                showWrapup,
                setShowWrapup,
                selectedWrapupList,
                // wrapupCategoryListHandle,
                wrapupCategoryFullListHandle
            },
            selectedInteraction,
            getSingleHistoryConversation,
        } = useTbarContext();

        const {
            connectedTime,
            conversationId,
            phoneNumber,
            agentData,
            agents,
            customerData,
            customerDatas,
            consultDatas,
            voicemailData,
            isHistory,
            consultData,
            tBarStatus,
            isConference,
            fullConference,
            talkTime,
        } = useHandleInteractionData(selectedInteraction);
        const wrapupCategoryListHandle = useMemo(() => {
            if (agentData?.queue?.id) {
                return wrapupCategoryFullListHandle?.filter((item: any) => {
                    return item?.queueId === agentData?.queue?.id
                })[0]
            }
            return {
                items: wrapupCategoryFullListHandle?.flatMap(
                    (queueItem: any) => queueItem?.items || []
                )
            }
        }, [wrapupCategoryFullListHandle, agentData])
        const { getSingleActiveConversation, loading } = useGetCallDetail({
            setShowWrapup,
            selectInteraction,
        });
        const agentsWithoutWrapUpList = agents?.filter((agent: any) => {
            if (agent?.endTime) {
                return agent?.wrapup || agent?.wrapUps?.wrapUpList;
            } else if (agent?.connectedTime) {
                return !agent?.wrapup && !agent?.wrapUps?.wrapUpList;
            }
            return !agent?.wrapup && !agent?.wrapUps?.wrapUpList;
        });
        const handleWrapUpClose = () => {
            setShowWrapup(false);
        };

        useEffect(() => {
            if (cId) {
                getSingleActiveConversation(cId);
            }
        }, [cId, selectInteraction]);
        const handleWrapUpSubmit = async () => {
            const findItemById = (
                items: TWrapUpOption[],
                targetId?: string
            ): TWrapUpOption | undefined => {
                if (!targetId) return undefined;
                for (const item of items) {
                    if (item.id === targetId) return item;
                    if (item.items) {
                        const found = findItemById(item.items, targetId);
                        if (found) return found;
                    }
                }
                return undefined;
            };
            const wrapUpList = wrapUpFormState?.selectedItems
                .map((itemId) => {
                    const item = findItemById(
                        wrapupCategoryListHandle?.items || [],
                        itemId
                    );
                    if (!item) return null;
                    const itemRemark = wrapUpFormState.itemRemarks.find(
                        (r) => r.itemId === itemId
                    );
                    return {
                        wrapUpCode: item.code,
                        wrapUpName: item.name,
                        remark: itemRemark?.remark || '',
                    };
                })
                .filter(Boolean);

            const existedWrapup = agents?.every(
                (agent: any) =>
                    (agent?.wrapup && agent?.wrapup?.code) ||
                    (agent?.wrapUps?.wrapUpList?.length > 0 &&
                        agent?.wrapUps?.wrapUpList?.some((wrapup: any) => wrapup?.id))
            );

            const apiFunc = existedWrapup
                ? fireUpdateWrapupCode
                : fireSubmitWrapupCode;
            apiFunc(
                basePath,
                (wrapUpList as TSubmitWrapup[]) || [],
                (agents?.length > 1 ? agentsWithoutWrapUpList?.[0]?.id : agentData?.id) || selectLastActiveAgentId,
                conversationId || selectLastActiveConvId,
                wrapUpFormState?.notes,
                'disconnected'
            )
                .then((res) => {
                    if (res.data.isSuccess) {
                        setWrapUpParticipantIds(
                            [
                                ...wrapUpParticipantIds,
                                agentsWithoutWrapUpList?.[0]?.id,
                            ]?.filter(Boolean)
                        );
                        if (agentsWithoutWrapUpList?.length <= 1) {
                            setShowWrapup(false);
                        }
                        setWrapUpFormState(null);
                        getSingleHistoryConversation(conversationId);
                    }
                })
                .catch((error) => {
                    return toast({
                        variant: 'error',
                        title: 'Error',
                        description: `${error?.response?.data?.error}`,
                    });
                });
        };
        const handleWrapUpStateChange = (newState: TWrapUpFormState) => {
            setWrapUpFormState(newState);
        };

        useEffect(() => {
            if (location?.pathname?.includes('call')) {
                setConversationId(searchParams?.get('conversationId'));
            } else {
                setConversationId(null);
            }
            setWrapUpFormState(null);
        }, [searchParams, location]);
        useEffect(() => {
            const t = formatInteractionDuration(
                agentData?.connectedTime,
                agentData?.endTime
            );
            if (isHistory || tBarStatus == 'disconnected') {
                if (t) {
                    setTime(t);
                } else {
                    if (talkTime) {
                        setTime(talkTime);
                    } else {
                        setTime('00:00');
                    }
                }

                return;
            }
        }, [agentData, isHistory, tBarStatus]);
        useEffect(() => {
            if (isHistory || tBarStatus == 'disconnected') {
                return;
            }
            // if (!connectedTime) return;
            // Convert connectedTime to a Date object
            const initialDate = new Date(connectedTime || new Date());

            // Function to update the time
            const updateTimer = () => {
                const now = new Date();
                const diffInMs = Math.max(now.getTime() - initialDate.getTime(), 0);
                const diffInSeconds = Math.floor(diffInMs / 1000);
                // setCount(diffInMs);

                const minutes = Math.floor(diffInSeconds / 60);
                const seconds = diffInSeconds % 60;

                // Format time mm:ss
                const formattedTime = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                setTime(formattedTime);
            };
            updateTimer();
            // Set interval to update time every second
            const intervalId = setInterval(updateTimer, 1000);

            // Clear interval on component unmount
            return () => clearInterval(intervalId);
        }, [connectedTime, isHistory, tBarStatus]);
        function findAllParentIds(
            code: string,
            treeData: any[],
            currentId: string[],
            parentIds: string[],
            itemRemarks: any[],
            wrapUp: any
        ): { currentId: string[]; parentIds: string[]; itemRemarks: any[] } {
            // 辅助函数，用于递归遍历树节点
            function traverse(node: any, currentParents: string[] = []): void {
                if (node?.code === code) {
                    // 复制 wrapUp 对象，避免引用问题
                    const newWrapUp = { ...wrapUp };
                    newWrapUp.itemId = node.id;
                    itemRemarks?.push(newWrapUp);
                    currentId?.push(node?.id);
                    // 将当前节点的所有父节点 id 添加到 parentIds 中
                    parentIds.push(...currentParents);
                }
                if (node.items) {
                    for (const child of node.items) {
                        // 递归调用 traverse 函数，并将当前节点的 id 添加到 currentParents 中
                        traverse(child, [...currentParents, node.id]);
                    }
                }
            }

            // 遍历树的根节点
            for (const node of treeData) {
                traverse(node);
            }
            const uniqueIds = [...new Set([...parentIds, ...currentId])];
            return {
                currentId: currentId,
                parentIds: uniqueIds,
                itemRemarks: itemRemarks,
            };
        }

        useEffect(() => {
            let ids: any;
            const parentIds: string[] = [];
            const currentId: string[] = [];
            const itemRemarks: any[] = [];
            const allWrapUpLists = agents?.flatMap(
                (agent: any) => agent?.wrapUps?.wrapUpList || []
            );
            const validRemark = agents?.find((agent: any) => agent?.wrapUps?.remark)
                ?.wrapUps?.remark;

            allWrapUpLists
                ?.filter((item: any) => {
                    return item?.wrapUpCode !== '';
                })
                ?.forEach((wrapUp: any) => {
                    if (wrapupCategoryListHandle && wrapupCategoryListHandle?.items && wrapupCategoryListHandle?.items.length > 0) {
                        ids = findAllParentIds(
                            wrapUp?.wrapUpCode,
                            wrapupCategoryListHandle?.items,
                            currentId,
                            parentIds,
                            itemRemarks,
                            wrapUp
                        );
                    }

                });

            if (showWrapup && isHistory) {
                setWrapUpFormState({
                    selectedItems: ids?.parentIds || [],
                    notes:
                        validRemark ||
                        allWrapUpLists?.find((item: any) => {
                            return item?.wrapUpCode == '';
                        })?.remark,
                    expandedItems: ids?.parentIds || [],
                    itemRemarks: ids?.itemRemarks || [],
                    activeRemarkItems: ids?.currentId || [],
                });
            }
        }, [wrapupCategoryListHandle, showWrapup, isHistory]);
        return (
            <Panel
                loading={loading}
                containerClassName="h-full rounded-none relative"
                className="items-center"
            >
                {cId || cId !== '' ? (
                    <>
                        {cId === conversationId && (
                            <div className="flex flex-col w-full rounded-2xl bg-white gap-8">
                                <div className="flex w-full justify-between items-center border-b border-grey-200">
                                    <div className="w-full flex items-center justify-between">
                                        <div className="flex gap-6 shrink-0">
                                            <div className="py-2 px-2 text-body flex items-center justify-center gap-2">
                                                <div className="box-border flex items-center justify-center h-7 w-7 text-[#DEDEDE]  rounded-full border-2 border-[#DEDEDE]">
                                                    <Icon
                                                        name={'user'}
                                                        className="fill-black"
                                                    // size={64}
                                                    />
                                                </div>
                                                <div>{phoneNumber}</div>
                                            </div>
                                        </div>
                                        {isHistory ? (
                                            <>
                                                {!selectedInteraction?.missedCall && (
                                                    <div className="py-2 px-2 flex items-center justify-center gap-3">
                                                        <Tooltip
                                                            content="Wrap up"
                                                            trigger={
                                                                <button
                                                                    className={cn(
                                                                        'flex items-center hover:text-primary',
                                                                        showWrapup ? 'text-primary' : ''
                                                                    )}
                                                                    onClick={() => {
                                                                        if (showWrapup) {
                                                                            handleWrapUpClose();
                                                                        } else {
                                                                            setShowWrapup(true);
                                                                        }
                                                                    }}
                                                                >
                                                                    <Icon
                                                                        name="pencil"
                                                                        size={16}
                                                                    />
                                                                </button>}
                                                        />
                                                    </div>
                                                )}
                                            </>
                                        ) : cId !== conversationId ? (
                                            <></>
                                        ) : (
                                            !selectedInteraction?.missedCall && (
                                                <div className="py-2 px-2 flex items-center justify-center gap-3">
                                                    <p className="px-2  border-2 border-[#DEDEDE]">
                                                        {/* <Timer
                                                initialTime={connectedTime}
                                                // show={!!connectedTime}
                                            /> */}
                                                        {time}
                                                    </p>
                                                    <div>
                                                        <Tooltip
                                                            content="Wrap up"
                                                            trigger={
                                                                <button
                                                                    className="flex items-center hover:text-primary"
                                                                    onClick={() => {
                                                                        if (showWrapup) {
                                                                            setShowWrapup(false);
                                                                        } else {
                                                                            setShowWrapup(true);
                                                                        }
                                                                    }}
                                                                >
                                                                    <IconWrapup2
                                                                        size={'28'}
                                                                        className={cn(
                                                                            'hover:fill-current',
                                                                            showWrapup ? '!fill-primary' : ''
                                                                        )}
                                                                    />
                                                                </button>}
                                                        />
                                                    </div>
                                                    <Directory
                                                        setOpenDirectory={setOpenDirectory}
                                                        open={openDirectory}
                                                        title="Transfer"
                                                        disabled={
                                                            tBarStatus == 'disconnected' ||
                                                            isConference ||
                                                            fullConference
                                                        }
                                                    >
                                                        <IconTransferLine
                                                            size={'28'}
                                                            className={cn(
                                                                tBarStatus == 'disconnected'
                                                                    ? ''
                                                                    : 'hover:fill-current',
                                                                openDirectory && '!fill-primary'
                                                            )}
                                                            color={
                                                                tBarStatus == 'disconnected'
                                                                    ? '#7C7C7C'
                                                                    : '#000000'
                                                            }
                                                        />

                                                    </Directory>

                                                </div>
                                            )
                                        )}
                                    </div>
                                </div>
                            </div>
                        )}
                        {showWrapup ? (
                            <WrapUp
                                onRevert={handleWrapUpClose}
                                onSubmit={handleWrapUpSubmit}
                                formState={wrapUpFormState || initialState}
                                onFormStateChange={handleWrapUpStateChange}
                                wrapUpData={[
                                    wrapupCategoryListHandle
                                ]}
                                loading={false}
                                error={wrapUpError}
                                className={'w-full h-full overflow-y-auto'}
                                buttonClassName={'absolute bottom-4 right-0'}
                                isSubmit={tBarStatus == 'disconnected' || isHistory}
                            />
                        ) : (
                            <CallPanel
                                customerData={customerDatas}
                                consultData={consultDatas}
                                voicemailData={voicemailData}
                            />
                        )}
                    </>
                ) : (
                    <div className="w-full h-full flex flex-col items-center justify-center">
                        <IconEmptyRecords size="78" />
                        <div className="text-grey-500">No selected interaction.</div>
                    </div>
                )}
            </Panel>
        );
    },
    (prevProps: any, nextProps: any) => {
        // 自定义比较函数，只有当数据真正变化时才重渲染
        return (
            JSON.stringify(prevProps.selectInteraction) ===
            JSON.stringify(nextProps.selectInteraction)
        );
    }
);
