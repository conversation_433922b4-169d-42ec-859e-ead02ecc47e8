import Input from '@cdss-modules/design-system/components/_ui/Input';
import {
  Select,
  TItemProps,
} from '@cdss-modules/design-system/components/_ui/Select';
import { ChevronLeft } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import {
  validateGlobalPhoneNumber,
  removePhoneNumberSpaces,
} from '@cdss-modules/design-system/lib/utils';
import {
  Button,
  toast,
  Tooltip,
  useRole,
  useRouteHandler,
  useToast,
  useBlocking,
} from '@cdss-modules/design-system';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import { useCallControl } from '@cdss-modules/design-system/lib/hooks/useCallControl';
import { useOpenStationContext } from '@cdss-modules/design-system/context/StationContext';
import { useTbarContext } from '@cdss-modules/design-system/context/TBarContext';
import Directory from '@cdss-modules/design-system/components/_ui/Directory';
import { useMessageControl } from '@cdss-modules/design-system/lib/hooks/useMessageControl';
import { CreateMessagePayLoad } from '@cdss-modules/design-system/@types/Message';
import { microfrontends } from '@cdss/types/microfrontendsConfig';
import { messageOutboundEnableQueue } from '@cdss/types/microfrontendsConfig';
import { GetEmailQueue, NewEmailConversation } from '@cdss/lib/api';
import { CreateEmailPayLoad } from '@cdss/types/microfrontendsConfig';
interface TNewConversationContainer {
  returnEvent: (isNew: boolean) => void;
  queues: any[];
}
const NewConversationContainer = ({
  returnEvent,
  queues,
}: TNewConversationContainer) => {
  const { globalConfig }=useRole()
  const channelDefaultValue=globalConfig?.microfrontends?.["ctint-mf-cdss"]?.["newConversation"]?.["channel"]?.["defaultValue"]
  const [searchTerm, setSearchTerm] = useState('');
  const [channel, setChannel] = useState(channelDefaultValue||'');
  const [queue, setQueue] = useState('');
  const [number, setNumber] = useState('');
  const { call } = useCallControl();
  const { newWhatsAppMessageConversation } = useMessageControl();
  const { open, setOpen, openOption, setOpenOption } = useOpenStationContext();
  const [openDirectory, setOpenDirectory] = useState(false);
  const { dismiss } = useToast();
  const [selected, setSelected] = useState<any | null>(null);
  const {
    stationContext: { station, stationHandler },
  } = useTbarContext();
  const microfrontendsConfig: microfrontends = globalConfig?.microfrontends;
  const messageOutboundEnableQueue: messageOutboundEnableQueue[] =
    microfrontendsConfig?.['ctint-mf-cdss']?.[
      'message-outbound-enable-queue'
    ] || [];
  const useExistingConversation: boolean =
    microfrontendsConfig?.['ctint-mf-cdss']?.['useExistingConversation'] ||
    false;

  useEffect(() => {
    // console.log('New Conversation');
  }, []);
  const [toAddress, setToAddress] = useState<string>('');
  const [fromAddress, setFromAddress] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [emailAddressOptions, setEmailAddressOptions] = useState<TItemProps[]>(
    []
  );
  const { basePath } = useRouteHandler();
  const { handleTriggerBlocking } = useBlocking();
  useEffect(() => {
    const loadEmailAddresses = async () => {
      if (channel === 'email') {
        setLoading(true);
        try {
          const options = await getEmailAddressOptions();
          setEmailAddressOptions(options);
        } finally {
          setLoading(false);
        }
      }
    };
    loadEmailAddresses();
  }, [channel]);
  const getEmailAddressOptions = async (): Promise<TItemProps[]> => {
    try {
      const response: any = await GetEmailQueue(basePath);
      console.log(response.data.data);
      if (response?.data?.data) {
        // 检查 data.data
        return response.data.data.map((queue: any) => ({
          // 使用 data.data 来 map
          id: queue.defaultQueueId,
          label: queue.mailBoxAddress,
          value: queue.mailBoxAddress,
        }));
      } else {
        return [];
      }
    } catch (error) {
      console.error('Error fetching email addresses:', error);
      toast({
        variant: 'error',
        title: 'Error',
        description: 'Failed to load email addresses',
      });
      return [];
    }
  };

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return emailRegex.test(email);
  };
  const getQueuesItemForSelect = (): TItemProps[] => {
    //tips 这里传入的options要看channel 如果是是message 则需要再筛选一次 如果q.id===messageOutboundEnableQueue里面的q.id 才能加入到数组中
    const queueSelectData: TItemProps[] = [];
    queues.forEach((q) => {
      if (channel === 'WhatsApp') {
        const isQueueEnabled = messageOutboundEnableQueue.some(
          (enabledQueue: messageOutboundEnableQueue) =>
            enabledQueue.queueId === q.id
        );
        if (isQueueEnabled) {
          const itemProps: TItemProps = {
            id: q.id,
            label: q.name,
            value: q.id,
          };
          queueSelectData.push(itemProps);
        }
      } else {
        const itemProps: TItemProps = {
          id: q.id,
          label: q.name,
          value: q.id,
        };
        queueSelectData.push(itemProps);
      }
    });
    if (channel === 'call' && station && station?.id !== '') {
      return [
        {
          id: station?.id,
          label: 'My Direct Line(' + station?.name + ')',
          value: station?.id,
          isNotQueue: true,
        },
        ...queueSelectData,
      ];
    }
    return queueSelectData;
  };
  const makeCallByDifferentKey = () => {
    // 暂时不校验是否是真实电话号码
    // if (queue && validateGlobalPhoneNumber(number)) {
    if (queue && number && !selected) {
      const r = getQueuesItemForSelect().find((q) => q.id == queue);
      if (r?.isNotQueue) {
        return {
          phoneNumber: removePhoneNumberSpaces(number),
        };
      }
      return {
        phoneNumber: removePhoneNumberSpaces(number),
        callFromQueueId: queue,
      };
    } else {
      if (selected && selected?.id !== '') {
        const r = getQueuesItemForSelect().find((q) => q.id == queue);
        if (r?.isNotQueue) {
          if (selected?.isQueue) {
            return {
              callQueueId: selected?.id,
            };
          }
          return {
            callUserId: selected?.id,
          };
        } else {
          if (selected?.isQueue) {
            return {
              callQueueId: selected?.id,
              callFromQueueId: queue,
            };
          }
          return {
            callUserId: selected?.id,
            callFromQueueId: queue,
          };
        }
      }
    }
    return {};
  };
  const handleDial = () => {
    // 暂时不校验是否是真实电话号码
    // if (queue || (number && validateGlobalPhoneNumber(number))) {
    if (queue || number) {
      call(makeCallByDifferentKey());
    } else {
      return toast({
        variant: 'error',
        title: 'Error',
        description:
          'Invalid phone number format. Please use one of the following formats: +852 6xxxxxxx, +8526xxxxxxx, or 6xxxxxxx.',
      });
    }
  };
  const handleWhatsAppMessage = () => {
    if (queue || (number && validateGlobalPhoneNumber(number))) {
      const payload: CreateMessagePayLoad = {
        queueId: queue,
        toAddress: number,
        useExistingConversation: useExistingConversation,
      };
      newWhatsAppMessageConversation(payload);
    } else {
      return toast({
        variant: 'error',
        title: 'Error',
        description:
          'Invalid phone number format. Please use one of the following formats: +852 6xxxxxxx, +8526xxxxxxx, or 6xxxxxxx.',
      });
    }
  };
  const handleToastClose = () => {
    dismiss();
  };
  const selectStationTip = () => {
    return toast({
      variant: 'error',
      title: 'Warning',
      description: (
        <div className="flex flex-col w-full gap-2">
          <div>You have no phone selected and will not receive calls.</div>
          <div className="flex w-full gap-2">
            <Button
              variant={'primary'}
              size={'s'}
              onClick={() => {
                setOpenOption(true);
                setOpen(true);
                handleToastClose();
                stationHandler?.refetch();
              }}
            >
              Select Phone
            </Button>
            <Button
              variant={'blank'}
              size={'s'}
              onClick={() => {
                handleToastClose();
              }}
            >
              Close
            </Button>
          </div>
        </div>
      ),
    });
  };
  const handleEmail = async () => {
    if (!fromAddress) {
      return toast({
        variant: 'error',
        title: 'Error',
        description: 'Please select a From Address',
      });
    }

    // Split and validate all email addresses
    const emailAddresses = toAddress.split(';').map((email) => email.trim());
    const invalidEmails = emailAddresses.filter(
      (email) => !validateEmail(email)
    );

    if (invalidEmails.length > 0) {
      return toast({
        variant: 'error',
        title: 'Invalid Email Format',
        description: `The following emails are invalid: ${invalidEmails.join(', ')}`,
      });
    }

    // 查找所选fromAddress对应的queueId
    const selectedOption = emailAddressOptions.find(
      (option) => option.value === fromAddress
    );

    if (!selectedOption) {
      return toast({
        variant: 'error',
        title: 'Error',
        description: 'Cannot find queue ID for the selected From Address',
      });
    }

    // 检查每个邮箱地址是否被阻止
    // for (const email of emailAddresses) {
    const isBlocked = await handleTriggerBlocking(toAddress, 'email');
    if (isBlocked) {
      // 如果被阻止，handleTriggerBlocking 已经显示了弹窗，直接返回
      return;
    }
    // }

    const payload: CreateEmailPayLoad = {
      queueId: selectedOption.id,
      toAddress: toAddress,
      fromAddress: fromAddress,
    };

    console.log('Creating email conversation with payload:', payload);
    try {
      await NewEmailConversation(basePath, payload);
    } catch (error) {
      toast({
        variant: 'error',
        title: 'Error',
        description: 'Failed to create email conversation',
      });
    }
  };
  const submit = () => {
    if ((!station || station?.id === '') && channel == 'call') {
      selectStationTip();
      return;
    }
    if (channel == 'call') {
      handleDial();
    }
    if (channel == 'WhatsApp') {
      handleWhatsAppMessage();
    }
    if (channel == 'email') {
      handleEmail();
    }
    setChannel('');
    setQueue('');
    setNumber('');
    returnEvent(false);
    console.log(channel, queue, number);
  };
  const getSelectValue = (v: any) => {
    console.log('v----------', v);
    setSelected(v);
    // setNumber(v?.name)
    // setOpenDirectory(false)
  };
  const isButtonDisabled = () => {
    if (channel === 'email') {
      return !fromAddress || !toAddress;
    } else {
      return !queue || !number;
    }
  };
  const renderChannelSpecificFields = () => {
    if (channel === 'email') {
      return (
        <>
          <div className="mb-4 w-full">
            <div className="text-base font-medium mb-1">
              From Address<span className="text-red-500">*</span>
            </div>
            <Select
              optionClassName="w-full"
              placeholder="Please select an email address"
              options={emailAddressOptions}
              labelClassName="h-full text-remark text-lg"
              labelContainerClassName="h-10 w-full text-lg"
              isPagination={false}
              value={fromAddress}
              onChange={(value) => setFromAddress(value)}
            />
          </div>
          <div className="mb-4 w-full">
            <div className="text-base font-medium mb-1">
              To Address<span className="text-red-500">*</span>
            </div>
            <div className="flex flex-col">
              <Input
                size="m" // Use larger size
                value={toAddress}
                onChange={(value) => setToAddress(String(value))}
                placeholder="Type/Paste your email address(es). Split by colon (;) if multiple"
                containerClassName="h-16" // Taller input container
                className="text-base" // Larger text
              />
              {toAddress && (
                <div className="mt-1 text-xs">
                  {toAddress.split(';').map((email, index) => (
                    <span
                      key={index}
                      className={`px-1 ${validateEmail(email.trim()) ? 'text-green-600' : 'text-red-500'}`}
                    >
                      {email.trim()} {validateEmail(email.trim()) ? '✓' : '✗'}
                    </span>
                  ))}
                </div>
              )}
            </div>
          </div>
        </>
      );
    }

    return (
      <>
        <div className="mb-4 w-full">
          <div className="text-base font-medium mb-1">On behalf of</div>
          <Select
            optionClassName="w-full break-all"
            placeholder="Select Options"
            options={getQueuesItemForSelect()}
            showSearch={true}
            labelClassName="h-full text-remark text-lg"
            labelContainerClassName="h-10 w-full text-lg"
            isPagination={false}
            value={queue}
            onChange={(value) => setQueue(value)}
          />
        </div>
        <div className="mb-4 w-full">
          <div className="text-base font-medium mb-1">To</div>
          <Input
            size="s"
            value={number}
            onChange={(value) => {
              setOpenDirectory(true);
              setNumber(String(value));
            }}
            placeholder="Enter the name or number"
            afterIcon={
              channel === 'call' ? (
                <Directory
                  getSelectValue={getSelectValue}
                  searchValue={number}
                  setSearch={(v) => setNumber(v)}
                  isShowButton={false}
                  open={openDirectory}
                  setOpenDirectory={setOpenDirectory}
                >
                  <Tooltip
                    content="secondCall"
                    trigger={
                      <Icon
                        name="secondCall"
                        size={18}
                      />
                    }
                  />
                </Directory>
              ) : undefined
            }
            containerClassName="h-10"
          />
        </div>
      </>
    );
  };
  return (
    <div className="w-full h-full max-w-md p-4 bg-white">
      {/* Header */}
      <div className="flex items-center mb-6">
        <ChevronLeft
          className="w-5 h-5 mr-2 cursor-pointer"
          onClick={() => {
            returnEvent(false);
          }}
        />
        <h1 className="text-base font-medium">Start a new conversation</h1>
      </div>

      {/* Channel Selection */}
      <div className="mb-4 w-full">
        <div className="text-base font-medium mb-1 ">Channel</div>
        <Select
          optionClassName={'w-full'}
          placeholder="Select Options"
          options={[
            {
              id: 'call',
              label: 'Phone Call',
              value: 'call',
            },
            {
              id: 'WhatsApp',
              label: 'WhatsApp',
              value: 'WhatsApp',
            },
            {
              id: 'email',
              label: 'Email',
              value: 'email',
            },
          ]}
          labelClassName="h-full text-remark text-lg"
          labelContainerClassName="h-10 w-full text-lg"
          isPagination={false}
          value={channel}
          onChange={function (value: any): void {
            // console.log(value);
            setChannel(value);
          }}
        />
      </div>

      {renderChannelSpecificFields()}
      <button
        onClick={submit}
        className="w-full bg-black text-white py-3 px-4 rounded-lg font-medium mt-5"
        disabled={isButtonDisabled()}
      >
        Start a conversation now
      </button>
    </div>
  );
};

export default NewConversationContainer;
