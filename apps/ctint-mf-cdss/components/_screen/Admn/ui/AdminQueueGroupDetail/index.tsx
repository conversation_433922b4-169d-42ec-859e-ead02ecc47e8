import { toast, useCDSSAdmin, useRole } from '@cdss-modules/design-system';
import Button from '@cdss-modules/design-system/components/_ui/Button';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import { get2LettersFromName } from '@cdss-modules/design-system/lib/utils';
import { PenSquare, Save } from 'lucide-react';
import { Controller, FormProvider, useForm } from 'react-hook-form';
import EditableField from '../EditableField';
import { Fragment, useEffect, useState } from 'react';
import _ from 'lodash';
import { formatDateTime, TcustmCol } from '../AdminUserList';
import {
  AddGroup,
  GetQueueList,
  GetUserList,
  UpdateGroup,
} from '@cdss/lib/api';
import { basePath } from '@cdss/lib/appConfig';
import {
  CombinedRoleNGroupData,
  TAdminUserDataResp,
  TAdminUserGroupDataResp,
  TformComponent,
} from '@cdss/types/microfrontendsConfig';
import { TGroupPostData } from '@cdss/types/group';
import { useTranslation } from 'react-i18next';
import { CommonPermission } from '@cdss-modules/design-system/@types/CommonPermission';
import { usePermission } from '@cdss-modules/design-system/context/PremissionContext';

type TadminUserDetail = {
  showCols: TcustmCol[];
  setOpenedEntity: (data: any) => void;
  getUserQueueList: () => void;
};

export const AdminQueueGruopDetail = (props: TadminUserDetail) => {
  const { t } = useTranslation();
  const { updateOpenedEntity, openedEntity } = useCDSSAdmin();
  const [isEditing, setIsEditing] = useState(false);
  const [formLayout, setFormLayout] = useState<TformComponent[]>([]);
  const [combinedData, setCombinedData] = useState<CombinedRoleNGroupData>({
    queueNames: [],
    userNames: [],
  });
  const methods = useForm({});
  const { showCols } = props;

  const { globalConfig } = useRole();
  const { permissions } = usePermission();

  const { handleSubmit, control } = methods;
  const isNew = _.isEmpty(openedEntity?.entity);

  useEffect(() => {
    fetchQueuesData();
  }, []);

  const fetchQueuesData = async () => {
    try {
      const [queuesResult, usersResult] = await Promise.all([
        GetQueueList(basePath),
        GetUserList(basePath),
      ]);
      const queuesData: TAdminUserGroupDataResp = queuesResult.data;
      const usersData: TAdminUserDataResp = usersResult.data;
      const tempCombinedData = {
        queueNames: queuesData.data || [],
        userNames: usersData.data || [],
      };
      setCombinedData(tempCombinedData);
    } catch (error) {
      console.error('Get Role and Group data fail:', error);
    }
  };

  const onSubmit = async (data: any) => {
    const rs = matchQueueValues(data, combinedData);
    data = { ...data, ...rs, type: 'queue' } as TGroupPostData;
    let resp = null;
    if (isNew) {
      resp = await AddGroup(basePath, data);
    } else {
      resp = await UpdateGroup(basePath, data);
    }

    if (resp.status == 200 || resp.data.isSuccess) {
      toast({
        variant: 'success',
        title: 'User Saved',
        description: `New Queue Group Information was saved successfully!`,
      });
      props.getUserQueueList();
      if (!isEditing) {
        setIsEditing(true);
      } else {
        setIsEditing(false);
      }
      setTimeout(() => {
        props.setOpenedEntity(null);
      }, 1500);
    }
  };

  useEffect(() => {
    const tempFormLayout: TformComponent[] = [];
    showCols.map((col, index) => {
      const formComponent: TformComponent = {
        title: col.name,
        name: col.key,
        value: openedEntity.entity[col.key],
        readOnly: col.readOnly,
        filterType: col.filterType,
        require: col.require,
      };
      tempFormLayout.push(formComponent);
    });
    if (!openedEntity?.entity || isNew) {
      setIsEditing(true);
    }
    setFormLayout(tempFormLayout);
  }, [openedEntity, isNew]);
  const UserInitData = {
    id: '',
    name: '',
    state: 'active',
    queues: [
      {
        id: '',
        name: '',
        state: '',
      },
    ],
  };

  useEffect(() => {
    const sourceData = !isNew ? openedEntity?.entity : UserInitData;
    if (sourceData) {
      const inputVals = Object.fromEntries(
        formLayout.map((item) => [
          item.name,
          item.filterType === 'dateRange'
            ? formatDateTime(sourceData[item.name])
            : item.filterType === 'multipleSelect'
              ? handleMultipleSelectValue(sourceData[item.name])
              : sourceData[item.name] || '',
        ])
      );

      // 重置表单
      methods.reset(inputVals);
    }
  }, [formLayout]);

  const handleMultipleSelectValue = (value: string): string[] => {
    // console.log(value);
    if (value === '' || value === undefined) return [];
    return value.split(',');
  };

  const entity = openedEntity?.entity;
  const avatarLetters = (
    entity?.name ? get2LettersFromName(entity?.name || '') : 'NA'
  ) as string;
  return (
    <FormProvider {...methods}>
      <form
        onSubmit={handleSubmit(onSubmit)}
        className="flex flex-col w-full h-full px-2 pt-0 pb-6"
      >
        <div className="flex-1 h-0 flex flex-col gap-y-4 overflow-auto">
          <div className="flex flex-col gap-x-4 w-full h-0 flex-1">
            <div className="w-full relative flex items-center gap-x-2 justify-between py-2 z-30">
              <div className="w-full flex items-center gap-x-8">
                <div className="flex items-center gap-x-2">
                  <button
                    type="button"
                    onClick={() => updateOpenedEntity(null)}
                    className="inline-flex gap-x-2 items-center group/sop-back hover:text-primary"
                  >
                    <Icon
                      name="back"
                      className="inline-flex"
                    />
                    <div className="min-w-max">
                      {t('ctint-mf-user-admin.tab.queueGroup')}
                    </div>
                  </button>
                  <span className="text-black">/</span>
                  <div className="w-full">
                    <span className="text-black">
                      {entity?.name ||
                        t('ctint-mf-user-admin.tab.newQueueGroup')}
                    </span>
                  </div>
                </div>
              </div>
              {new CommonPermission(
                globalConfig,
                permissions
              ).isPermissionEnabled('ctint-mf-admin', 'user', 'edit') && (
                <div className="flex items-center gap-x-2">
                  {isEditing ? (
                    <>
                      <Button
                        size="s"
                        //type="submit"
                        variant="secondary"
                        onClick={() => {
                          if (isNew) {
                            updateOpenedEntity(null);
                          } else {
                            setIsEditing(false);
                          }
                        }}
                      >
                        <span className="whitespace-nowrap">
                          {t('ctint-mf-user-admin.formAction.discard')}
                        </span>
                      </Button>
                      <Button
                        beforeIcon={<Save />}
                        size="s"
                        type="submit"
                      >
                        <span className="whitespace-nowrap">
                          {t('ctint-mf-user-admin.formAction.save')}
                        </span>
                      </Button>
                    </>
                  ) : (
                    <Button
                      beforeIcon={<PenSquare />}
                      size="s"
                      type="button"
                      onClick={() => setIsEditing(true)}
                      disabled={!entity.isAllowEdit}
                    >
                      <span className="whitespace-nowrap">
                        {entity.isAllowEdit
                          ? t('ctint-mf-user-admin.formAction.edit')
                          : t('ctint-mf-user-admin.formAction.notAllowEdit')}
                      </span>
                    </Button>
                  )}
                </div>
              )}
            </div>
            <div className="flex items-start py-2 gap-x-8 justify-center">
              <div className="flex">
                <div className="flex flex-wrap gap-6 max-w-[1400px]">
                  {formLayout.map((v: any) => (
                    <Fragment key={v.name}>
                      <EditableField
                        isEditing={isEditing}
                        combinedData={combinedData}
                        {...v}
                      />
                    </Fragment>
                  ))}
                  <Controller
                    name="id"
                    control={control}
                    defaultValue={entity?.id}
                    render={({ field }) => (
                      <>
                        <input
                          type="hidden"
                          {...field}
                        />
                      </>
                    )}
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="flex justify-between gap-y-4 pt-4 h-10 overflow-auto hidden">
            {avatarLetters}
            {JSON.stringify(entity)}
          </div>
        </div>
      </form>
    </FormProvider>
  );

  function matchQueueValues(firstJson: any, secondJson: any) {
    const result = {
      queues: [],
      users: [],
    };
    if (firstJson.queueNames && Array.isArray(firstJson.queueNames)) {
      firstJson.queueNames.forEach((groups: any) => {
        const matchedQueue = secondJson.queueNames.find(
          (queue: any) => queue.name === groups
        );
        if (matchedQueue) {
          (result.queues as any).push(matchedQueue);
        }
      });
    }
    // 處理userNames
    if (firstJson.userNames && Array.isArray(firstJson.userNames)) {
      firstJson.userNames.forEach((userName: any) => {
        const matchedRole: any = secondJson.userNames.find(
          (user: any) => user.name === userName
        );
        if (matchedRole) {
          (result.users as any).push(matchedRole);
        }
      });
    }
    return result;
  }
};
