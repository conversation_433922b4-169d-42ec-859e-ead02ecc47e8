import { SortingButton } from '@cdss-modules/design-system';
import { Tooltip } from '@cdss-modules/design-system/components/_ui/Tooltip';
import { Plus } from 'lucide-react';
import { formatDateTime } from '../AdminUserList';
import { ColumnDef } from '@tanstack/react-table';
import { TAdminUserData } from '../../../../../types/microfrontendsConfig';
import { useRole } from '@cdss-modules/design-system';
import { usePermission } from '@cdss-modules/design-system/context/PremissionContext';
import { CommonPermission } from '@cdss-modules/design-system/@types/CommonPermission';

export const generateColumns = (
  columns: string[],
  showColumns: string[],
  columnOrdering: string[],
  sortOrder: any,
  setSortOrder: (input: any) => void,
  openEntity: (id?: string) => void
) => {
  const { globalConfig } = useRole();
  const { permissions } = usePermission();
  const actionCol = {
    id: 'action',
    maxSize:50,
    header: () => (
      <div className="flex justify-start">
        {new CommonPermission(globalConfig, permissions).isPermissionEnabled(
          'ctint-mf-admin',
          'user',
          'create'
        ) && (
          <Tooltip
            trigger={
              <button
                type="button"
                className="bg-primary text-white rounded-full size-6 flex-none flex justify-center items-center animate-pulse hover:animate-none"
                onClick={() => {
                  openEntity();
                }}
              >
                <Plus size={18} />
              </button>
            }
            content="Add New Entity"
          />
        )}
      </div>
    ),
    cell: (
      <div className="flex gap-x-2 z-0 justify-end">
        {/* <AdminActionMenu /> */}
      </div>
    ),
    enableSorting: false,
    enableHiding: false,
  };

  const orderedColumns = columnOrdering
    ? columns.sort((firstEl, secondEl) => {
        const firstIndex = columnOrdering?.findIndex(
          (item: any) => item.name === firstEl
        );
        const secondIndex = columnOrdering?.findIndex(
          (item: any) => item.name === secondEl
        );
        return firstIndex - secondIndex;
      })
    : columns;

  const formattedColumns = orderedColumns.map(
    (customColumn: string, index: number) => {
      const isDate =
        customColumn === 'createTime' || customColumn === 'updateTime';
      return {
        id: customColumn,
        accessorKey: customColumn,
        header: ({ column }: any) => {
          const columnGetToggleSortingHandler =
            column.getToggleSortingHandler();
          return (
            <SortingButton
              sorting={
                sortOrder?.[customColumn]
                  ? sortOrder?.[customColumn] === 'ASC'
                    ? 'asc'
                    : 'desc'
                  : false
              }
              onClick={async (e) => {
                columnGetToggleSortingHandler?.(e);
                const targetSortOrder =
                  sortOrder?.[customColumn] === 'ASC' ? 'DESC' : 'ASC';
                setSortOrder({
                  [customColumn]: targetSortOrder,
                });
              }}
            >
              {showColumns[index]}
            </SortingButton>
          );
        },
        cell: ({ row }: any) => {
          let val = row.getValue(customColumn) as any;
          if (isDate) {
            val = val ? formatDateTime(val) : '';
          }
          if (customColumn === 'name') return <strong>{val}</strong>;
          return <>{val != '' && val != undefined ? val : '——'}</>;
        },
        ...(isDate && {
          filterFn: (row: any, columnId: any, filterValue: any) => {
            const cellDate = new Date(row.getValue(columnId));
            const startDate = new Date(filterValue.start);
            const endDate = new Date(filterValue.end);
            if (startDate && endDate) {
              return cellDate >= startDate && cellDate <= endDate;
            } else if (startDate) {
              return cellDate >= startDate;
            } else if (endDate) {
              return cellDate <= endDate;
            }
            return true;
          },
        }),
        ...(customColumn === 'state' && {
          filterFn: (row: any, columnId: any, filterValue: any) => {
            return row.getValue(columnId) === filterValue ? true : false;
          },
        }),
      } as unknown as ColumnDef<TAdminUserData>;
    }
  );

  return [actionCol, ...formattedColumns];
};
