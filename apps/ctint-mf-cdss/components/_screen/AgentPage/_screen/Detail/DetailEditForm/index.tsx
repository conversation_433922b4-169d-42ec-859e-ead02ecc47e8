'use client';
import React from 'react';
import { Controller, FormProvider, useForm } from 'react-hook-form';
import Button from '@cdss-modules/design-system/components/_ui/Button';
// import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import IconSetting from '@cdss-modules/design-system/components/_ui/Icon/IconSetting';
import { Select } from '@cdss-modules/design-system/components/_ui/Select';
import IconUsers from '@cdss-modules/design-system/components/_ui/Icon/IconUsers';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import IconInteractionId from '@cdss-modules/design-system/components/_ui/Icon/IconInteractionId';
import IconQueue from '@cdss-modules/design-system/components/_ui/Icon/IconQueue';
import { t } from 'i18next';
import { Save } from 'lucide-react';

const langOptions = [
  {
    id: 'english',
    label: 'English',
    value: 'English',
  },
  {
    id: 'cantonese',
    label: 'Cantonese',
    value: 'Cantonese',
  },
  {
    id: 'mandarin',
    label: 'Mandarin',
    value: 'Mandarin',
  },
];

const investmentOrderTypeOptions = [
  {
    id: 'ELI',
    label: 'Investment Order – ELI',
    value: 'ELI',
  },
  {
    id: 'ELN',
    label: 'Investment Order – ELN',
    value: 'ELN',
  },
  {
    id: 'HYD',
    label: 'Investment Order – FXLD-HYD',
    value: 'HYD',
  },
  {
    id: 'PPD',
    label: 'Investment Order – FXLD-PPD',
    value: 'PPD',
  },
  // Investment Order – FXLD-Swap Deposit (Code = SD)
  {
    id: 'SD',
    label: 'Investment Order – FXLD-Swap Deposit',
    value: 'SD',
  },
  // Investment Order – Bonds (Code = BONDS)
  {
    id: 'BONDS',
    label: 'Investment Order – Bonds',
    value: 'BONDS',
  },
  // Investment Order – Mutual Fund (MF)
  {
    id: 'MF',
    label: 'Investment Order – Mutual Fund',
    value: 'MF',
  },
  // Investment Order – Others (OTHER)
  {
    id: 'OTHER',
    label: 'Investment Order – Others',
    value: 'OTHER',
  },
  // Insurance (Code = INS)
  {
    id: 'INS',
    label: 'Insurance',
    value: 'INS',
  },
  // General Enquiry (Code = GEN)
  {
    id: 'GEN',
    label: 'General Enquiry',
    value: 'GEN',
  },
];

type CallDetailEditFormProps = {
  details: any;
  processSubmit: any;
  isLoading: boolean;
};

export default function CallDetailEditForm({
  details,
  processSubmit,
  isLoading,
}: CallDetailEditFormProps) {
  // use the form hook
  const methods = useForm({
    defaultValues: details,
  });
  const { handleSubmit, control, formState } = methods;

  return (
    <FormProvider {...methods}>
      <form
        className="p-4"
        onSubmit={handleSubmit(processSubmit)}
      >
        <section>
          <div className="flex flex-row-reverse items-center gap-x-2">
            <Button
              variant="primary"
              type="submit"
              size={'s'}
              beforeIcon={<Save />}
              disabled={isLoading}
            >
              Save
            </Button>

            <Button
              variant="secondary"
              size={'s'}
            >
              Cancel
            </Button>
          </div>
          <div className="grid grid-cols-2 gap-6">
            {/* Language */}
            <section className="flex flex-row flex-wrap gap-2">
              <div className="row-span-2">
                <IconSetting
                  alt="Language"
                  size="24"
                />
              </div>
              <section className="grid grid-flow-col grid-rows-2 gap-2">
                <div className="col-span-11 row-span-1">
                  <strong>{t('ctint-mf-cdss.callPatch.language')}:</strong>
                </div>
                <div className="row-span-1 col-span-11 font-light text-[#636363]">
                  <Controller
                    name="lang"
                    control={control}
                    //rules={{ required: true }}
                    render={({ field }) => (
                      <Select
                        placeholder="Language"
                        mode="single"
                        isPagination={false}
                        defaultValue={details?.lang}
                        options={langOptions}
                        //   showSearch={true}
                        value={field.value}
                        onChange={(e) => field.onChange(e)}
                      />
                    )}
                  />
                </div>
              </section>
            </section>

            {/* Officer code */}
            <section className="flex flex-row flex-wrap gap-2">
              <div className="row-span-2">
                <IconUsers
                  alt="Language"
                  size="24"
                  color="black"
                />
              </div>
              <section className="grid grid-flow-col grid-rows-2 gap-2">
                <div className="col-span-11 row-span-1">
                  <strong>{t('ctint-mf-cdss.callPatch.officerCode')}:</strong>
                </div>
                <div className="row-span-1 col-span-11 font-light text-[#636363]">
                  <Controller
                    name="officerCode"
                    control={control}
                    render={({ field }) => (
                      <Input
                        size="s"
                        placeholder="Officer Code"
                        defaultValue={details?.officerCode || ''}
                        className="w-full"
                        {...field}
                      />
                    )}
                  />
                </div>
              </section>
            </section>

            {/* SA Reference number*/}
            <section className="flex flex-row flex-wrap gap-2">
              <div className="row-span-2">
                <IconInteractionId
                  alt="SaReferenceNumber"
                  size="24"
                  color="black"
                />
              </div>
              <section className="grid grid-flow-col grid-rows-2 gap-2">
                <div className="col-span-11 row-span-1">
                  <strong>
                    {t('ctint-mf-cdss.callPatch.saReferenceNumber')}:
                  </strong>
                </div>
                <div className="row-span-1 col-span-11 font-light text-[#636363]">
                  <Controller
                    name="saReferenceNumber"
                    control={control}
                    render={({ field }) => (
                      <Input
                        size="s"
                        placeholder="SA Reference Number"
                        defaultValue={details?.saReferenceNumber || ''}
                        className="w-full"
                        {...field}
                      />
                    )}
                  />
                </div>
              </section>
            </section>

            {/* Customer CIF */}
            <section className="flex flex-row flex-wrap gap-2">
              <div className="row-span-2">
                <IconUsers
                  alt="CustomerCIF"
                  size="24"
                  color="black"
                />
              </div>
              <section className="grid grid-flow-col grid-rows-2 gap-2">
                <div className="col-span-11 row-span-1">
                  <strong>{t('ctint-mf-cdss.callPatch.customerCIF')}:</strong>
                </div>
                <div className="row-span-1 col-span-11 font-light text-[#636363]">
                  {/* <p>{details?.customerCIF || 'N/A'}</p> */}
                  <Controller
                    name="customerCIF"
                    control={control}
                    render={({ field }) => (
                      <Input
                        size="s"
                        placeholder="Customer CIF"
                        defaultValue={details?.customerCIF || ''}
                        // value={field.value}
                        className="w-full"
                        {...field}
                      />
                    )}
                  />
                </div>
              </section>
            </section>
            {/* investment order type */}
            <section className="flex flex-row flex-wrap gap-2">
              <div className="row-span-2">
                <IconQueue
                  alt="InvestmentOrderType"
                  size="24"
                  color="black"
                />
              </div>
              <section className="grid grid-flow-col grid-rows-2 gap-2">
                <div className="col-span-11 row-span-1">
                  <strong>
                    {t('ctint-mf-cdss.callPatch.investmentOrderType')}:
                  </strong>
                </div>
                <div className="row-span-1 col-span-11 font-light text-[#636363]">
                  {/* <p>
                                        {details?.investmentOrderType || 'N/A'}
                                      </p> */}
                  <Controller
                    name="investmentOrderType"
                    control={control}
                    render={({ field }) => (
                      <Select
                        placeholder="Investment Order Type"
                        mode="single"
                        isPagination={false}
                        defaultValue={details?.investmentOrderType}
                        options={investmentOrderTypeOptions}
                        value={field.value}
                        onChange={(e) => field.onChange(e)}
                      />
                    )}
                  />
                </div>
              </section>
            </section>
            {/* Investment account */}
            <section className="flex flex-row flex-wrap gap-2">
              <div className="row-span-2">
                <IconQueue
                  alt="InvestmentAccount"
                  size="24"
                  color="black"
                />
              </div>
              <section className="grid grid-flow-col grid-rows-2 gap-2">
                <div className="col-span-11 row-span-1">
                  <strong>
                    {t('ctint-mf-cdss.callPatch.investmentAccount')}:
                  </strong>
                </div>
                <div className="row-span-1 col-span-11 font-light text-[#636363]">
                  {/* <p>
                                        {details?.investmentAccount || 'N/A'}
                                      </p> */}
                  <Controller
                    name="investmentAccount"
                    control={control}
                    render={({ field }) => (
                      <Input
                        size="s"
                        placeholder="Investment Account"
                        defaultValue={details?.investmentAccount || ''}
                        className="w-full"
                        {...field}
                      />
                    )}
                  />
                </div>
              </section>
            </section>
            {/* Product name 1 */}
            <section className="flex flex-row flex-wrap gap-2">
              <div className="row-span-2">
                <IconInteractionId
                  alt="ProductName1"
                  size="24"
                  color="black"
                />
              </div>
              <section className="grid grid-flow-col grid-rows-2 gap-2">
                <div className="col-span-11 row-span-1">
                  <strong>{t('ctint-mf-cdss.callPatch.productName1')}:</strong>
                </div>
                <div className="row-span-1 col-span-11 font-light text-[#636363]">
                  {/* <p>{details?.productName1 || 'N/A'}</p> */}
                  <Controller
                    name="productName1"
                    control={control}
                    render={({ field }) => (
                      <Input
                        size="s"
                        placeholder="Product Name 1"
                        defaultValue={details?.productName1 || ''}
                        className="w-full"
                        {...field}
                      />
                    )}
                  />
                </div>
              </section>
            </section>
            {/* Product name 2 */}
            <section className="flex flex-row flex-wrap gap-2">
              <div className="row-span-2">
                <IconInteractionId
                  alt="ProductName2"
                  size="24"
                  color="black"
                />
              </div>
              <section className="grid grid-flow-col grid-rows-2 gap-2">
                <div className="col-span-11 row-span-1">
                  <strong>{t('ctint-mf-cdss.callPatch.productName2')}:</strong>
                </div>
                <div className="row-span-1 col-span-11 font-light text-[#636363]">
                  {/* <p>{details?.productName2 || 'N/A'}</p> */}
                  <Controller
                    name="productName2"
                    control={control}
                    render={({ field }) => (
                      <Input
                        size="s"
                        placeholder="Product Name 2"
                        defaultValue={details?.productName2 || ''}
                        className="w-full"
                        {...field}
                      />
                    )}
                  />
                </div>
              </section>
            </section>
            {/* Product name 3 */}
            <section className="flex flex-row flex-wrap gap-2">
              <div className="row-span-2">
                <IconInteractionId
                  alt="ProductName3"
                  size="24"
                  color="black"
                />
              </div>
              <section className="grid grid-flow-col grid-rows-2 gap-2">
                <div className="col-span-11 row-span-1">
                  <strong>{t('ctint-mf-cdss.callPatch.productName3')}:</strong>
                </div>
                <div className="row-span-1 col-span-11 font-light text-[#636363]">
                  {/* <p>{details?.productName3 || 'N/A'}</p> */}
                  <Controller
                    name="productName3"
                    control={control}
                    render={({ field }) => (
                      <Input
                        size="s"
                        placeholder="Product Name 3"
                        defaultValue={details?.productName3 || ''}
                        className="w-full"
                        {...field}
                      />
                    )}
                  />
                </div>
              </section>
            </section>
          </div>
        </section>
      </form>
    </FormProvider>
  );
}
