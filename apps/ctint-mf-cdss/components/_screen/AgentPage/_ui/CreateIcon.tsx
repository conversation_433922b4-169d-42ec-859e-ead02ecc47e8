import { lazy, Suspense } from 'react';

type TCreateIconProps = {
  iconType?: string;
};

const TIcon = {
  voice: 'IconVoice',
  callback: 'IconCallback',
  chat: 'IconChat',
  email: 'IconEmail',
  message: 'IconMessage',
};
// 按需动态导入图标
const CreateIcon = ({ iconType }: TCreateIconProps) => {
  const iconFileKey = TIcon?.[iconType as keyof typeof TIcon];
  if (!iconType || !iconFileKey) {
    return null;
  }

  const IconComponent = lazy(
    () =>
      import(
        `@cdss-modules/design-system/components/_ui/Icon/${iconFileKey}.tsx`
      )
  );
  return (
    <div>
      <Suspense fallback={<div>Loading...</div>}>
        <IconComponent
          size={16}
          color="black"
        />
      </Suspense>
    </div>
  );
};

export { CreateIcon, TIcon };
