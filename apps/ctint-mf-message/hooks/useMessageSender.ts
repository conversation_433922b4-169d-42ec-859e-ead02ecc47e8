import { useCallback } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { sendOutboundMessage } from 'apps/ctint-mf-message/lib/api';
// @ts-ignore
import { useConversationStore } from 'cdss/store/conversation';
import {
  CDSSMessage,
  MediaItem,
} from '@cdss-modules/design-system/@types/Message';

export const useMessageSender = (basePath: string | undefined) => {
  const { handleRealTimeMessage, updateMessageStatus } = useConversationStore();

  const sendTextMessage = useCallback(
    async (
      conversationId: string,
      textBody: string,
      referenceMessage?: CDSSMessage
    ) => {
      const reqId = uuidv4();
      const cleanedText = textBody.replace(/^\s+|\s+$/g, '');

      if (!cleanedText) return false;

      // 创建临时消息显示在UI上
      const tempMessage: CDSSMessage = {
        id: `temp-${Date.now()}`,
        conversationId,
        participantId: '',
        originalPlatform: '',
        platform: '',
        platformMessageId: ``,
        externalMessageId: '',
        channelId: '',
        direction: 'outbound',
        messengerType: 'text',
        category: '',
        type: 'text',
        userName: '',
        userId: '',
        fromAddress: '',
        fromName: '',
        toAddress: '',
        toName: '',
        timestamp: new Date(),
        textBody: cleanedText,
        status: 'sending',
        metadata: '',
        tenant: '',
        reqId: reqId,
        // 如果有引用消息，添加到临时消息中
        reference: referenceMessage,
        referenceId: referenceMessage?.id,
      };

      // 更新UI
      handleRealTimeMessage(conversationId, [tempMessage]);

      try {
        // 构建发送消息的载荷
        const messagePayload: any = {
          type: 'text',
          text: cleanedText,
        };

        // 如果有引用消息，添加 reference 字段
        if (referenceMessage?.id) {
          messagePayload.reference = {
            messageId: referenceMessage.id,
          };
        }

        const sendResponse = await sendOutboundMessage(
          basePath,
          conversationId,
          reqId,
          messagePayload
        );

        const realTimeMessage: CDSSMessage[] = [
          {
            id: sendResponse.data.data.id,
            conversationId: sendResponse.data.data.conversationId,
            participantId: '',
            originalPlatform: sendResponse.data.data.originalPlatform,
            platform: sendResponse.data.data.platform,
            platformMessageId: sendResponse.data.data.platformMessageId,
            externalMessageId: '',
            channelId: sendResponse.data.data.channelId,
            direction: sendResponse.data.data.direction,
            messengerType: sendResponse.data.data.messengerType,
            category: sendResponse.data.data.category,
            type: sendResponse.data.data.type,
            userName: sendResponse.data.data.userName,
            userId: sendResponse.data.data.userId,
            fromAddress: sendResponse.data.data.fromAddress,
            fromName: sendResponse.data.data.fromName,
            toAddress: sendResponse.data.data.toAddress,
            toName: sendResponse.data.data.toName,
            timestamp: new Date(sendResponse.data.data.timestamp),
            textBody: sendResponse.data.data.textBody,
            status: sendResponse.data.data.status,
            metadata: sendResponse.data.data.rawPayload,
            tenant: sendResponse.data.data.tenant,
            reqId: sendResponse.data.reqId,
            medias: sendResponse.data.data.medias,
            // 保留引用消息信息
            reference: referenceMessage,
            referenceId: referenceMessage?.id,
          },
        ];

        handleRealTimeMessage(conversationId, realTimeMessage);
        return true;
      } catch (error) {
        console.error('Error sending message:', error);
        updateMessageStatus(conversationId, reqId, 'failed');
        return false;
      }
    },
    [basePath, handleRealTimeMessage, updateMessageStatus]
  );

  const sendMediaMessage = useCallback(
    async (
      conversationId: string,
      mediaItemId: string,
      mediaItem: MediaItem,
      referenceMessage?: CDSSMessage
    ) => {
      if (!mediaItemId || !mediaItem) return false;
      console.log('sendMediaMessage item', mediaItem);
      const reqId = uuidv4();

      // 创建临时媒体消息显示在UI上，使用已有的媒体信息
      const tempMessage: CDSSMessage = {
        id: `temp-${Date.now()}`,
        conversationId,
        participantId: '',
        originalPlatform: '',
        platform: '',
        platformMessageId: ``,
        externalMessageId: '',
        channelId: '',
        direction: 'outbound',
        messengerType: 'media',
        category: '',
        type: 'media',
        userName: '',
        userId: '',
        fromAddress: '',
        fromName: '',
        toAddress: '',
        toName: '',
        timestamp: new Date(),
        textBody: '',
        status: 'sending',
        metadata: '',
        tenant: '',
        reqId: reqId,
        // 直接使用完整的媒体信息进行回显
        medias: [mediaItem],
        // 如果有引用消息，添加到临时消息中
        reference: referenceMessage,
        referenceId: referenceMessage?.id,
      };

      // 更新UI显示临时消息
      handleRealTimeMessage(conversationId, [tempMessage]);

      try {
        // 构建发送消息的载荷
        const messagePayload: any = {
          type: 'media',
          media: { id: mediaItemId },
        };

        // 如果有引用消息，添加 reference 字段
        if (referenceMessage?.id) {
          messagePayload.reference = {
            messageId: referenceMessage.id,
          };
        }

        const sendResponse = await sendOutboundMessage(
          basePath,
          conversationId,
          reqId,
          messagePayload
        );

        // 构建实际发送成功后的消息对象
        const realTimeMessage: CDSSMessage[] = [
          {
            id: sendResponse.data.data.id,
            conversationId: sendResponse.data.data.conversationId,
            participantId: '',
            originalPlatform: sendResponse.data.data.originalPlatform,
            platform: sendResponse.data.data.platform,
            platformMessageId: sendResponse.data.data.platformMessageId,
            externalMessageId: '',
            channelId: sendResponse.data.data.channelId,
            direction: sendResponse.data.data.direction,
            messengerType: sendResponse.data.data.messengerType || 'media',
            category: sendResponse.data.data.category,
            type: sendResponse.data.data.type || 'media',
            userName: sendResponse.data.data.userName,
            userId: sendResponse.data.data.userId,
            fromAddress: sendResponse.data.data.fromAddress,
            fromName: sendResponse.data.data.fromName,
            toAddress: sendResponse.data.data.toAddress,
            toName: sendResponse.data.data.toName,
            timestamp: new Date(sendResponse.data.data.timestamp),
            textBody: sendResponse.data.data.textBody || '',
            status: sendResponse.data.data.status,
            metadata: sendResponse.data.data.rawPayload,
            tenant: sendResponse.data.data.tenant,
            reqId: sendResponse.data.reqId,
            // 使用API返回的媒体列表，如果没有则使用我们的临时媒体
            medias: sendResponse.data.data.medias ||
              sendResponse.data.data.mediaList || [mediaItem],
            // 保留引用消息信息
            reference: referenceMessage,
            referenceId: referenceMessage?.id,
          },
        ];

        // 更新UI显示成功发送的消息
        handleRealTimeMessage(conversationId, realTimeMessage);
        return true;
      } catch (error) {
        console.error('Error sending media message:', error);
        // 更新消息状态为失败
        updateMessageStatus(conversationId, reqId, 'failed');
        return false;
      }
    },
    [basePath, handleRealTimeMessage, updateMessageStatus]
  );

  // 添加一个新的函数来批量发送媒体消息
  const sendMediaMessages = useCallback(
    async (
      conversationId: string,
      mediaItems: MediaItem[],
      referenceMessage?: CDSSMessage
    ) => {
      if (!mediaItems || mediaItems.length === 0) return false;

      // 依次发送每个媒体项
      const results = [];
      for (const mediaItem of mediaItems) {
        const result = await sendMediaMessage(
          conversationId,
          mediaItem.id,
          mediaItem,
          referenceMessage
        );
        results.push(result);
      }

      // 如果所有发送都成功，返回true
      return results.every((result) => result === true);
    },
    [sendMediaMessage]
  );

  return {
    sendTextMessage,
    sendMediaMessage,
    sendMediaMessages,
  };
};
