// eslint-disable-next-line @typescript-eslint/ban-ts-comment
import React, { useState, useRef, useEffect } from 'react';
import { toast, useRouteHandler } from '@cdss-modules/design-system';
//@ts-ignore
import { useConversationStore } from 'cdss/store/conversation';
import { CDSSMessage } from '@cdss-modules/design-system/@types/Message';
import { Search } from 'lucide-react';
import type {
  WABATemplatesResponse,
  WABATemplate,
  Component,
  BodyComponent,
  URLButton,
  HeaderComponent,
} from '../../../../@types/waTemplate';
import {
  getTemplateList,
  sendOutboundMessage,
  uploadMedia,
} from '../../../../lib/api';
import TemplatePreview from './TemplatePreview';
import {
  Parameter,
  PreviewTemplate,
  TMPayload,
  TemplateComponent,
} from '../../../../@types/waTemplateRequest';
import { v4 as uuidv4 } from 'uuid';

interface TemplatePageProps {
  conversationId: string;
  waba: string;
  className?: string;
  containerClassName?: string;
  searchContainerClassName?: string;
  contentContainerClassName?: string;
  inputFormClassName?: string;
  dropdownClassName?: string;
  buttonClassName?: string;
  onSendSuccess?: () => void;
  onSendError?: (error: Error) => void;
  isDNC?: boolean;
}

const TemplatePage: React.FC<TemplatePageProps> = ({
  conversationId,
  waba,
  className = '',
  containerClassName = 'flex flex-col max-h-full overflow-hidden border-2',
  searchContainerClassName = 'p-3',
  contentContainerClassName = 'flex-1 p-3 overflow-y-auto',
  inputFormClassName = 'space-y-4',
  dropdownClassName = 'absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-48 overflow-y-auto',
  buttonClassName = 'w-full py-2 rounded-lg transition-colors',
  onSendSuccess,
  onSendError,
  isDNC,
}) => {
  const { handleRealTimeMessage, updateMessageStatus, clearReferenceMessage } =
    useConversationStore();
  const [templates, setTemplates] = useState<WABATemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [isUploading, setIsUploading] = useState(false);
  const [isSending, setIsSending] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<WABATemplate | null>(
    null
  );
  const [previewTemplate, setPreviewTemplate] =
    useState<PreviewTemplate | null>(null);
  const [headerComponent, setHeaderComponent] =
    useState<TemplateComponent | null>(null);
  const [bodyComponent, setBodyComponent] = useState<TemplateComponent | null>(
    null
  );
  const [buttonComponents, setButtonComponents] = useState<TemplateComponent[]>(
    []
  );

  const searchRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { basePath } = useRouteHandler();
  // 获取 store 的清除引用消息方法
  const referenceMessageRef = useRef<CDSSMessage | undefined>();

  // Fetch template list
  useEffect(() => {
    console.log('DNC template', isDNC);
    const fetchTemplates = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await getTemplateList(basePath, waba);
        const data = response.data.data as WABATemplatesResponse;

        // Filter templates to only include approved ones
        const approvedTemplates = data.waba_templates.filter(
          (template) => template.status === 'approved'
        );

        setTemplates(approvedTemplates);
      } catch (err) {
        const errorObj =
          err instanceof Error ? err : new Error('Failed to fetch templates');
        setError(errorObj);
        if (onSendError) {
          onSendError(errorObj);
        }
      } finally {
        setLoading(false);
      }
    };
    fetchTemplates();
  }, [basePath, waba]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        searchRef.current &&
        !searchRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  //订阅referenceMessage
  useEffect(() => {
    // 订阅referenceMessage的变化
    const unsubscribeReference = useConversationStore.subscribe(
      (state: any) => state.referenceMessage,
      (newReferenceMessage: any) => {
        if (newReferenceMessage && newReferenceMessage.id) {
          referenceMessageRef.current = newReferenceMessage;
        } else if (!newReferenceMessage || !newReferenceMessage.id) {
          // 当 store 中的 referenceMessage 被清除时，也清除 ref
          referenceMessageRef.current = undefined;
        }
      }
    );
    // 返回清理函数
    return unsubscribeReference;
  }, []); // 空依赖数组，只在组件挂载时执行一次

  // Reset all states
  const resetComponent = () => {
    setPreviewTemplate(null);
    setSelectedTemplate(null);
    setHeaderComponent(null);
    setBodyComponent(null);
    setButtonComponents([]);
    setSearchQuery('');
    setIsDropdownOpen(false);
  };

  // Extract placeholder numbers
  const getPlaceholderNumbers = (text = ''): number[] => {
    const matches = text.match(/{{(\d+)}}/g) || [];
    return matches.map((match) =>
      parseInt(match.match(/{{(\d+)}}/)?.[1] || '0')
    );
  };

  // Initialize components state
  const initializeComponents = (template: WABATemplate) => {
    let newHeaderComponent: TemplateComponent | null = null;
    let newBodyComponent: TemplateComponent | null = null;
    const newButtonComponents: TemplateComponent[] = [];

    template.components.forEach((component) => {
      switch (component.type) {
        case 'HEADER':
          if (component.format === 'TEXT') {
            const placeholders = getPlaceholderNumbers(component.text);
            if (placeholders.length > 0) {
              newHeaderComponent = {
                type: 'header',
                parameters: placeholders.map(() => ({
                  type: 'text' as const,
                  text: '',
                })),
              };
            }
          } else if (component.format === 'IMAGE') {
            newHeaderComponent = {
              type: 'header',
              parameters: [
                {
                  type: 'image',
                  image: { id: '', link: '' },
                },
              ],
            };
          }
          break;

        case 'BODY': {
          const bodyPlaceholders = getPlaceholderNumbers(component.text);
          if (bodyPlaceholders.length > 0) {
            newBodyComponent = {
              type: 'body',
              parameters: bodyPlaceholders.map(
                (): Parameter => ({
                  type: 'text' as const,
                  text: '',
                })
              ),
            };
          }
          break;
        }
        case 'BUTTONS':
          if ('buttons' in component) {
            component.buttons.forEach((button, index) => {
              // Only process URL type buttons
              if (button.type === 'URL') {
                // If button URL has placeholders, create corresponding component
                const placeholders = getPlaceholderNumbers(button.url || '');
                if (placeholders.length > 0) {
                  newButtonComponents.push({
                    type: 'button',
                    sub_type: 'url',
                    index,
                    parameters: placeholders.map(() => ({
                      type: 'text' as const,
                      text: '',
                    })),
                  });
                }
              }
            });
          }
          break;
      }
    });

    setHeaderComponent(newHeaderComponent);
    setBodyComponent(newBodyComponent);
    setButtonComponents(newButtonComponents);
  };

  // Handle template selection
  const handleTemplateSelect = (template: WABATemplate) => {
    setSelectedTemplate(template);
    setSearchQuery(template.name);
    setIsDropdownOpen(false);
    initializeComponents(template);
  };

  // Handle image upload
  const handleImageUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (!file || !selectedTemplate) return;

    if (!file.type.startsWith('image/')) {
      toast({
        title: 'Error',
        description: 'Please select an image file',
        variant: 'error',
      });
      return;
    }

    if (file.size > 5 * 1024 * 1024) {
      toast({
        title: 'Error',
        description: 'Image size should be less than 5MB',
        variant: 'error',
      });
      return;
    }

    setIsUploading(true);

    try {
      const response = await uploadMedia(basePath, conversationId, file);
      const newHeaderComponent: TemplateComponent = {
        type: 'header',
        parameters: [
          {
            type: 'image',
            image: {
              id: response.data.data.id,
              link: response.data.data.url,
            },
          },
        ],
      };
      setHeaderComponent(newHeaderComponent);
    } catch (error) {
      console.error('Upload failed:', error);
      toast({
        title: 'Error',
        description: 'Failed to upload image. Please try again.',
        variant: 'error',
      });
      if (error instanceof Error && onSendError) {
        onSendError(error);
      }
    } finally {
      setIsUploading(false);
    }
  };

  // Render input form
  const renderInputForm = () => {
    if (!selectedTemplate) return null;

    return (
      <div className={inputFormClassName}>
        {/* Header input area */}
        {headerComponent?.type === 'header' &&
          headerComponent.parameters.map(
            (param, index) =>
              param.type === 'text' && (
                <div
                  key={`header-${index}`}
                  className="p-4 border border-gray-200 rounded-lg"
                >
                  <label className="block mb-2 text-sm font-medium text-gray-700">
                    Header Variable {index + 1}
                  </label>
                  <input
                    type="text"
                    value={param.text || ''}
                    onChange={(e) => {
                      setHeaderComponent((prev) => {
                        if (!prev) return null;
                        const newParams = [...prev.parameters];
                        newParams[index] = {
                          ...newParams[index],
                          text: e.target.value,
                        };
                        return { ...prev, parameters: newParams };
                      });
                    }}
                    className="w-full p-2 border border-gray-200 rounded focus:outline-none focus:ring-1 focus:ring-orange-400"
                    placeholder="Enter value"
                  />
                </div>
              )
          )}

        {/* Body input area */}
        {bodyComponent?.type === 'body' &&
          bodyComponent.parameters.map(
            (param, index) =>
              param.type === 'text' && (
                <div
                  key={`body-${index}`}
                  className="p-4 border border-gray-200 rounded-lg"
                >
                  <label className="block mb-2 text-sm font-medium text-gray-700">
                    Body Variable {index + 1}
                  </label>
                  <input
                    type="text"
                    value={param.text || ''}
                    onChange={(e) => {
                      setBodyComponent((prev) => {
                        if (!prev) return null;
                        const newParams = [...prev.parameters];
                        newParams[index] = {
                          ...newParams[index],
                          text: e.target.value,
                        };
                        return { ...prev, parameters: newParams };
                      });
                    }}
                    className="w-full p-2 border border-gray-200 rounded focus:outline-none focus:ring-1 focus:ring-orange-400"
                    placeholder="Enter value"
                  />
                </div>
              )
          )}

        {/* Button input area */}
        {buttonComponents.map((buttonComponent, buttonIndex) =>
          buttonComponent.parameters.map(
            (param, paramIndex) =>
              param.type === 'text' && (
                <div
                  key={`button-${buttonIndex}-${paramIndex}`}
                  className="p-4 border border-gray-200 rounded-lg"
                >
                  <label className="block mb-2 text-sm font-medium text-gray-700">
                    Button index {buttonComponent.index} Variable{' '}
                    {paramIndex + 1}
                  </label>
                  <input
                    type="text"
                    value={param.text || ''}
                    onChange={(e) => {
                      setButtonComponents((prev) => {
                        const newComponents = [...prev];
                        const newParams = [
                          ...newComponents[buttonIndex].parameters,
                        ];
                        newParams[paramIndex] = {
                          ...newParams[paramIndex],
                          text: e.target.value,
                        };
                        newComponents[buttonIndex] = {
                          ...newComponents[buttonIndex],
                          parameters: newParams,
                        };
                        return newComponents;
                      });
                    }}
                    className="w-full p-2 border border-gray-200 rounded focus:outline-none focus:ring-1 focus:ring-orange-400"
                    placeholder="Enter value"
                  />
                </div>
              )
          )
        )}
      </div>
    );
  };

  // Filter templates
  const filteredTemplates = templates.filter((template) =>
    template.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Process template
  const processTemplate = (
    template: WABATemplate,
    headerComp: TemplateComponent | null,
    bodyComp: TemplateComponent | null,
    buttonComps: TemplateComponent[] | null
  ): { components: Component[] } => {
    const newComponents = template.components.map((component) => {
      switch (component.type) {
        case 'HEADER': {
          // For HEADER type, exclude specific example field
          const { example, ...headerRest } = component as HeaderComponent;
          if (headerRest.format === 'TEXT' && headerComp?.parameters) {
            let processedText = headerRest.text || '';
            headerComp.parameters.forEach((param, index) => {
              if (param.type === 'text' && param.text) {
                processedText = processedText.replace(
                  `{{${index + 1}}}`,
                  param.text
                );
              }
            });
            return { ...headerRest, text: processedText };
          } else if (
            headerRest.format === 'IMAGE' &&
            headerComp?.parameters[0]?.type === 'image'
          ) {
            return {
              ...headerRest,
              image: {
                id: headerComp.parameters[0].image?.id || '',
                link: headerComp.parameters[0].image?.link || '',
              },
            };
          }
          return headerRest;
        }

        case 'BODY': {
          // For BODY type, exclude specific example field
          const { example, ...bodyRest } = component as BodyComponent;
          if (bodyComp?.parameters) {
            let processedText = bodyRest.text || '';
            bodyComp.parameters.forEach((param, index) => {
              if (param.type === 'text' && param.text) {
                processedText = processedText.replace(
                  `{{${index + 1}}}`,
                  param.text
                );
              }
            });
            return { ...bodyRest, text: processedText };
          }
          return bodyRest;
        }

        case 'BUTTONS': {
          // For BUTTONS type, no need to exclude example (since button component itself doesn't have example field)
          if ('buttons' in component && buttonComps?.length) {
            const processedButtons = component.buttons.map(
              (button, buttonIndex) => {
                if (button.type === 'URL') {
                  // Only exclude example field for URL type buttons
                  const { example: urlExample, ...urlButtonRest } =
                    button as URLButton;
                  const buttonComp = buttonComps.find(
                    (b) => b.index === buttonIndex
                  );
                  if (buttonComp?.parameters) {
                    let processedUrl = urlButtonRest.url || '';
                    buttonComp.parameters.forEach((param, index) => {
                      if (param.type === 'text' && param.text) {
                        processedUrl = processedUrl.replace(
                          `{{${index + 1}}}`,
                          param.text
                        );
                      }
                    });
                    return { ...urlButtonRest, url: processedUrl };
                  }
                  return urlButtonRest;
                }
                return button; // Return other button types directly, since they don't have example field
              }
            );
            return { ...component, buttons: processedButtons };
          }
          return component;
        }

        default:
          // For other component types (e.g. FOOTER), return original component
          return component;
      }
    });

    return { components: newComponents };
  };

  const validateInputs = () => {
    // Check header inputs
    if (headerComponent?.parameters) {
      for (const param of headerComponent.parameters) {
        if (param.type === 'text' && !param.text) {
          return 'Please fill in all header variables';
        }
        if (param.type === 'image' && !param.image?.link) {
          return 'Please upload a header image';
        }
      }
    }

    // Check body inputs
    if (bodyComponent?.parameters) {
      for (const param of bodyComponent.parameters) {
        if (param.type === 'text' && !param.text) {
          return 'Please fill in all body variables';
        }
      }
    }

    // Check button inputs
    if (buttonComponents?.length) {
      for (const button of buttonComponents) {
        for (const param of button.parameters) {
          if (param.type === 'text' && !param.text) {
            return `Please fill in all button variables for button ${(button.index ?? 0) + 1}`;
          }
        }
      }
    }

    return null;
  };

  // Handle send button click
  const handleSendClick = async () => {
    if (!selectedTemplate || isUploading) return;

    // Input validation
    const validationError = validateInputs();
    if (validationError) {
      toast({
        title: 'Error',
        description: validationError,
        variant: 'error',
      });
      return;
    }

    // Create modified components array, for image only include link
    const modifiedComponents: TemplateComponent[] = [];

    if (headerComponent) {
      if (
        headerComponent.type === 'header' &&
        headerComponent.parameters[0]?.type === 'image' &&
        headerComponent.parameters[0].image?.link
      ) {
        // For image parameters, only keep link
        modifiedComponents.push({
          type: 'header',
          parameters: [
            {
              type: 'image',
              image: {
                link: headerComponent.parameters[0].image.link,
              },
            },
          ],
        });
      } else {
        modifiedComponents.push(headerComponent);
      }
    }

    if (bodyComponent) modifiedComponents.push(bodyComponent);

    // 对按钮组件进行特殊处理，对URL参数进行编码
    if (buttonComponents?.length) {
      const encodedButtonComponents = buttonComponents.map((button) => {
        if (button.sub_type === 'url' && button.parameters) {
          return {
            ...button,
            parameters: button.parameters.map((param) => {
              if (param.type === 'text' && param.text) {
                // 对URL中的参数进行编码
                return {
                  ...param,
                  text: encodeURIComponent(param.text),
                };
              }
              return param;
            }),
          };
        }
        return button;
      });
      modifiedComponents.push(...encodedButtonComponents);
    }

    // Use original components to process preview template (keep id in preview)
    const processedTemplate = processTemplate(
      selectedTemplate,
      headerComponent,
      bodyComponent,
      buttonComponents
    );
    setPreviewTemplate(processedTemplate);
    // Handle sending message
    await handleMessageSend(
      selectedTemplate,
      modifiedComponents,
      processedTemplate
    );
  };

  const handleMessageSend = async (
    template: WABATemplate,
    modifiedComponents: TemplateComponent[],
    processedTemplate: PreviewTemplate
  ) => {
    const reqId = uuidv4();

    try {
      setIsSending(true);
      const payload: TMPayload = {
        type: 'whatsappTemplate',
        whatsappTemplate: {
          payload: {
            type: 'template',
            template: {
              name: template.name,
              language: {
                code: template.language,
              },
              components: modifiedComponents,
            },
          },
          preview: processedTemplate,
          metaData: {},
        },
      };

      const sendResponse = await sendOutboundMessage(
        basePath,
        conversationId,
        reqId,
        payload
      );

      // Update store after successful send
      const realTimeMessage: CDSSMessage[] = [
        {
          id: sendResponse.data.data.id,
          conversationId: sendResponse.data.data.conversationId,
          participantId: '',
          originalPlatform: sendResponse.data.data.originalPlatform,
          platform: sendResponse.data.data.platform,
          platformMessageId: sendResponse.data.data.platformMessageId,
          externalMessageId: '',
          channelId: sendResponse.data.data.channelId,
          direction: sendResponse.data.data.direction,
          messengerType: sendResponse.data.data.messengerType,
          category: sendResponse.data.data.category,
          type: sendResponse.data.data.type,
          userName: sendResponse.data.data.userName,
          userId: sendResponse.data.data.userId,
          fromAddress: sendResponse.data.data.fromAddress,
          fromName: sendResponse.data.data.fromName,
          toAddress: sendResponse.data.data.toAddress,
          toName: sendResponse.data.data.toName,
          timestamp: new Date(sendResponse.data.data.timestamp),
          textBody: sendResponse.data.data.textBody,
          status: sendResponse.data.data.status,
          metadata: sendResponse.data.data.rawPayload,
          tenant: sendResponse.data.data.tenant,
          reqId: sendResponse.data.reqId,
          medias: sendResponse.data.data.medias,
        },
      ];

      handleRealTimeMessage(conversationId, realTimeMessage);
      toast({
        title: 'Success',
        description: 'Send template message successfully',
        variant: 'success',
      });

      if (onSendSuccess) {
        onSendSuccess();
      }

      resetComponent();
      return true;
    } catch (error) {
      console.error('Error sending message:', error);
      toast({
        title: 'Error',
        description: 'Send template message failed',
        variant: 'error',
      });
      updateMessageStatus(conversationId, reqId, 'failed');

      if (error instanceof Error && onSendError) {
        onSendError(error);
      }

      return false;
    } finally {
      setIsSending(false);
    }
  };

  return (
    <div className={`${containerClassName} ${className}`}>
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleImageUpload}
        className="hidden"
      />

      <div
        className={searchContainerClassName}
        ref={searchRef}
      >
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-4 w-4 text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Select a template"
            className="w-full pl-9 pr-3 py-2 text-sm border border-gray-200 rounded-lg focus:outline-none focus:ring-1 focus:ring-orange-400"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onFocus={() => setIsDropdownOpen(true)}
          />

          {/* Template dropdown list */}
          {isDropdownOpen && (
            <div className={dropdownClassName}>
              {loading ? (
                <div className="p-2.5 text-sm text-gray-500">
                  Loading templates...
                </div>
              ) : error ? (
                <div className="p-2.5 text-sm text-red-500">
                  Error loading templates
                </div>
              ) : filteredTemplates.length > 0 ? (
                filteredTemplates.map((template) => (
                  <div
                    key={template.id}
                    className="p-2.5 cursor-pointer text-gray-800 hover:bg-orange-50 hover:text-orange-500 transition-colors duration-150"
                    onClick={() => handleTemplateSelect(template)}
                  >
                    <p className="text-sm">{template.name}</p>
                    <p className="text-xs text-gray-500">{template.category}</p>
                  </div>
                ))
              ) : (
                <div className="p-2.5 text-sm text-gray-500">
                  No templates found
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Main content area */}
      <div className={contentContainerClassName}>
        {selectedTemplate && (
          <div className="space-y-4">
            {/* Preview area */}
            <TemplatePreview
              headerComponent={headerComponent}
              bodyComponent={bodyComponent}
              buttonComponents={buttonComponents}
              selectedTemplate={selectedTemplate}
              onImageClick={(e) => {
                e.preventDefault();
                if (!isUploading) {
                  fileInputRef.current?.click();
                }
              }}
              onReselect={(e) => {
                e.preventDefault();
                if (!isUploading) {
                  if (headerComponent) {
                    setHeaderComponent({
                      ...headerComponent,
                      parameters: [
                        {
                          type: 'image',
                          image: { id: '', link: '' },
                        },
                      ],
                    });
                  }
                  fileInputRef.current?.click();
                }
              }}
              mode="edit"
            />

            {/* Render input form */}
            {renderInputForm()}

            {/* Send button */}
            <button
              className={`${buttonClassName} ${
                isUploading || isSending || isDNC
                  ? 'bg-gray-400 cursor-not-allowed'
                  : 'bg-black text-white hover:bg-gray-800'
              }`}
              onClick={handleSendClick}
              disabled={isUploading || isSending || isDNC}
            >
              {isUploading
                ? 'Uploading...'
                : isSending
                  ? 'Sending...'
                  : isDNC
                    ? 'Do Not Call'
                    : 'Send Now'}
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default TemplatePage;
