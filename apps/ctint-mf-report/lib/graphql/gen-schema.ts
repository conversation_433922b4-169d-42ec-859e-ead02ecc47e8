import * as graphqlCodegen from '@graphql-codegen/cli';
import * as dotenv from 'dotenv';
dotenv.config();

async function main() {
  const graphqlEndpoint = `${process.env.NEXT_PUBLIC_GRAPHQL_URL}/graphql`;

  await graphqlCodegen.generate({
    schema: {
      [graphqlEndpoint]: {
        method: 'GET',
      },
    },
    // documents: ['./app/**/*.{ts,tsx}'],
    generates: {
      './lib/graphql/schema.ts': {
        plugins: ['typescript', 'typescript-operations'],
        config: {
          skipTypename: true,
          avoidOptionals: true,
          scalars: {
            Date: 'string',
            DateTime: 'string',
            Json: 'unknown',
            Long: 'number',
          },
        },
      },
    },
  });
}

main();
