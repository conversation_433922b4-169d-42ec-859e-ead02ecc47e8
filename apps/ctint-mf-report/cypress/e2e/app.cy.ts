describe('ctint-mf-report', () => {
  beforeEach(() => {
    window.localStorage.setItem(
      'cdss-auth-token',
      'Gs6On9NJ+6I4MpJXuugiUqD6Ndv9Zvr5IZ3K/P5YAz7qS23ccR0b6l+5/BYk9D9y+7L07RCTw1hIEriz4+K1pUwL4g=='
    );
    cy.visit('/');
    cy.getAllRecordings();
  });

  it('should display home title = filter', () => {
    cy.visit('/');
    // Custom command example, see `../support/commands.ts` file
    cy.get('[data-testid="cypress-panel-title-filter"]').should('be.visible');

    // Check if the element with data-testid="home-title" is displayed
    // cy.get('[data-testid="home-title"]').should('be.visible');
  });
});
