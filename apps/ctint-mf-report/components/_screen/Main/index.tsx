'use client';

import { WhitePanel } from '@cdss-modules/design-system/components/_ui/WhitePanel';
import RadixTabs from '@cdss-modules/design-system/components/_ui/RadixTabs';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import GenerateReportPanel from '../../ui/GenerateReportPanel';
import GenerateRecordPanel from '../../ui/GenerateRecordPanel';

/**
 * 報告主頁
 */
const ReportMain: React.FC = () => {
  const { t } = useTranslation();

  /**
   * 報告主頁的Tabs
   */
  const tabs = [
    {
      tabName: 'report',
      label: t('ctint-mf-report.main.tab.report'),
      content: <GenerateReportPanel />,
    },
    {
      tabName: 'record',
      label: t('ctint-mf-report.main.tab.record'),
      content: <GenerateRecordPanel />,
    },
  ];

  return (
    <WhitePanel className="h-full overflow-auto">
      <RadixTabs
        defaultOpenTab="report"
        tabs={tabs}
      />
    </WhitePanel>
  );
};

export default ReportMain;
