{"name": "ctint-mf-report", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/ctint-mf-report", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/next:build", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"root": "apps/ctint-mf-report", "outputPath": "dist/apps/ctint-mf-report"}, "configurations": {"development": {}, "production": {}}}, "serve": {"executor": "@nx/next:server", "defaultConfiguration": "development", "options": {"buildTarget": "ctint-mf-report:build", "dev": true, "port": 4302, "host": "0.0.0.0"}, "configurations": {"development": {"buildTarget": "ctint-mf-report:build:development", "dev": true}, "production": {"buildTarget": "ctint-mf-report:build:production", "dev": false}}}, "export": {"executor": "@nx/next:export", "options": {"buildTarget": "ctint-mf-report:build:production"}}, "test": {"executor": "@nx/jest:jest", "outputs": ["coverage/apps/ctint-mf-report"], "options": {"jestConfig": "apps/ctint-mf-report/jest.config.ts", "passWithNoTests": true}}, "lint": {"executor": "@nx/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/ctint-mf-report/**/*.{ts,tsx,js,jsx}"]}}}}