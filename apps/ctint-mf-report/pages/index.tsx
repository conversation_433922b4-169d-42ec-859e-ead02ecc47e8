import {
  PageRenderer,
  TglobalConfig,
  Toaster,
} from '@cdss-modules/design-system';
import ReportMain from '../components/_screen/Main';
import ReportDetail from '../components/_screen/Detail';
import { basePath, mfName } from '../lib/appConfig';
import loadGlobalConfig from '@cdss-modules/design-system/lib/globalConfig';
import { CommonPermissionWrapper } from '@cdss-modules/design-system/components/_other/PermissionWrapper/CommonPermissionWrapper';
import { CommonPermission } from '@cdss-modules/design-system/@types/CommonPermission';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

export const Page = () => {
  const queryClient = new QueryClient();
  return (
    <div className="relative p-6 w-full h-screen">
      <PageRenderer
        routes={[
          {
            path: '/',
            group: 'report',
            component: (
              // 這裡的permissionHandler是自定義的，用來控制是否顯示PlaybackModule
              <CommonPermissionWrapper
                customerPermissionHandler={(
                  globalConfig: TglobalConfig,
                  permissions: string[]
                ) => {
                  return true;
                }}
              >
                <QueryClientProvider client={queryClient}>
                  <ReportMain />
                  <Toaster />
                </QueryClientProvider>
              </CommonPermissionWrapper>
            ),
            subroutes: [
              {
                path: '/detail',
                component: (
                  // 這裡的permissionHandler是自定義的，用來控制是否顯示PlaybackModuleDetail
                  <CommonPermissionWrapper
                    customerPermissionHandler={(
                      globalConfig: TglobalConfig,
                      permissions: string[]
                    ) => {
                      return true;
                    }}
                  >
                    <QueryClientProvider client={queryClient}>
                      <ReportDetail />
                      <Toaster />
                    </QueryClientProvider>
                  </CommonPermissionWrapper>
                ),
              },
            ],
          },
        ]}
        basePath={basePath}
      />
    </div>
  );
};

export const getServerSideProps = async () => {
  const globalConfig = loadGlobalConfig(mfName);
  const publicEnvVars = Object.keys(process.env).reduce(
    (publicVars: any, key) => {
      if (key.startsWith('CDSS_PUBLIC_')) {
        publicVars[key] = process.env[key];
      }
      return publicVars;
    },
    {}
  ) as any;
  return {
    props: {
      globalConfig,
      publicEnvVars,
    },
  };
};

export default Page;
