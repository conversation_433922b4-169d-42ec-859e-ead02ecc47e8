auth:
  headers:
  - name: traceId
  - name: tenant
  - name: sourceId
  - name: previousId
  - name: cdss_authorization
logger:
  headers:
  - name: traceId
  - name: tenant
  - name: sourceId
  - name: previousId
pureengage:
  host: http://*************:8090
  recordsLimit: 200
genesys-cloud:
  environment: mypurecloud.com.au
  apiHost: https://api.mypurecloud.com.au
  authHost: https://login.mypurecloud.com.au
loginTotal: 1
missingTotal: 3
poolTotal: 10000
heartbeatTime: 30
socketType: CDSS
recording:
  mediaSource:
  - aws
ctint-dab:
  auth:
    host: http://ctint-dab-auth-service.cdss-data-ctint.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /graphql
    active: true
  config:
    host: http://ctint-dab-config-service.cdss-data-ctint.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /graphql
    active: true
  conv:
    host: http://ctint-dab-conv-service.cdss-data-ctint.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath:
    active: true
  user:
    host: http://ctint-dab-user-service.cdss-data-ctint.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /graphql
    active: true
  dataSync:
    host: http://ctint-dab-datasync-service.cdss-data-ctint.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /graphql
    active: true  
  callControl:
    host: http://ctint-dab-call-control-service.cdss-data-ctint.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    restBasepath: /api
    graphBasepath: /graphql
    active: true
  jobWorker:
    host: http://ctint-dab-job-service.cdss-data-ctint.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    restBasepath: /api
    graphBasepath: /graphql
    active: true
  common:
    host: http://ctint-graphql-service.cdss-data-ctint.svc.cluster.local:8110 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /ctint/query
    active: true
  auditLog:
    host: http://ctint-dab-audit-log-service.cdss-data-ctint.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /graphql
    active: true
  qm:
    host: http://ctint-dab-qm-service.cdss-data-ctint.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /graphql
    active: true
ctint-state:
  auth:
    host: http://ctint-state-auth-service.cdss-data-ctint.svc.cluster.local:35000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /v1.0/state/ctint-state-auth
    active: true
  cdssmf:
    host: http://ctint-state-cdssmf-service.cdss-data-ctint.svc.cluster.local:35010
    basepath: /v1.0/state/ctint-state-cdssmf
    active: true
  session-manager:
    host: http://ctint-state-session-manager-service.cdss-data-ctint.svc.cluster.local:35020
    basepath: /v1.0/state/ctint-state-session-manager
    active: true
portals:
- ctint-mf-cpp
services:
  ctint-stt:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/stt
    active: true
    provider: 
    - astri1.5
    healthcheck: /healthcheck
  ctint-nlp:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/nlp
    active: true
    provider: 
    - astri
    healthcheck: /healthcheck
  ctint-auth:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/auth
    active: true
    provider: # pure_engage / genesys_cloud / ctint-dab-auth / ctint-state-auth
    - genesys-cloud
    - ctint-dab-auth
    - ctint-state-auth
    healthcheck: /healthcheck
  ctint-conv:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/conv
    active: true
    provider: 
    - pureengage
    - ctint-dab-conv
    healthcheck: /healthcheck
    criteriaSearch:
    - labelEn: Type
      labelCh: 媒體類型
      value: mediaType
      filterType: select
      active: true
      isMetaData: false
    - labelEn: Users
      labelCh: 用戶資訊
      value: users
      filterType: input
      active: true
      isMetaData: false
    - labelEn: Start Time
      labelCh: 開始時間
      value: conversationStart
      filterType: date
      active: true
      isMetaData: false
    - labelEn: End Time
      labelCh: 結束時間
      value: conversationEnd
      filterType: date
      active: true
      isMetaData: false
    - labelEn: Conversation ID
      labelCh: 對話 ID
      value: conversationId
      filterType: input
      active: false
      isMetaData: false
    - labelEn: ANI
      labelCh: 來電號碼
      value: ani
      filterType: input
      active: false
      isMetaData: false            
    - labelEn: DNIS
      labelCh: 撥號號碼
      value: dnis
      filterType: input
      active: false
      isMetaData: false
    - labelEn: Duration
      labelCh: 持續時間
      value: conversationDuration
      filterType: compare
      active: true
      isMetaData: false
    - labelEn: Queues
      labelCh: 队列
      value: queues
      filterType: input
      active: true
      isMetaData: false
    - labelEn: Media Source
      labelCh: 媒體來源
      value: recordingMediaType
      filterType: input
      active: false
      isMetaData: false
    - labelEn: Customer Remote
      labelCh: 客户远程
      value: customerRemote
      filterType: input
      active: true
      isMetaData: false
    - labelEn: Wrapups
      labelCh: 总结代码
      value: wrapups
      filterType: input
      active: true
      isMetaData: false 
    - labelEn: Provider
      labelCh: 提供者
      value: provider
      filterType: input
      active: false
      isMetaData: false 
    - labelEn: Recording
      labelCh: 是否录音
      value: recording
      filterType: bool
      active: false
      isMetaData: false
    - labelEn: Direction
      labelCh: 方向
      value: direction
      filterType: select
      active: true
      isMetaData: false 
    - labelEn: FinalResult
      labelCh: 最终结果
      value: finalResult
      filterType: select
      active: true
      isMetaData: false
    - labelEn: accountId
      labelCh: 账号Id
      value: accountId
      filterType: input
      active: false
      isMetaData: true
  ctint-config:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/config
    active: true
    provider: 
    - pureengage
    - ctint-dab-config
    healthcheck: /healthcheck
  ctint-session:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/session
    active: true
    provider: 
    - ctint-state-cdssmf
    healthcheck: /healthcheck
  ctint-session-manager:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/session-manager
    active: true
    healthcheck: /healthcheck
  ctint-cdss-ws:
    host: https://test-ctint-cdss-ws-webapp.azurewebsites.net
    basepath: /ctint-cdss-ws
    active: true
    healthcheck: /healthcheck
  ctint-ccsp-ws:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/ccsp-ws
    active: true
    healthcheck: /healthcheck
  ctint-call-control:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/call-control
    active: true
    healthcheck: /healthcheck
  ctint-user:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/user
    active: true
    healthcheck: /healthcheck
  ctint-datasync-hub:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/datasync-hub
    active: true
    healthcheck: /healthcheck
  ctint-job-worker:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/job-worker
    active: true
    healthcheck: /healthcheck
  ctint-job-worker-auto-qm:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/job-worker-auto-qm
    active: true
    healthcheck: /healthcheck
  ctint-job-engine:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/job-engine
    active: true
    healthcheck: /healthcheck
  ctint-qm:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/qm
    active: true
    provider:
    healthcheck: /healthcheck
microfrontends:
  ctint-mf-cdss:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/mf-cdss
  ctint-mf-interaction:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/mf-interaction
    permissions:
    - ctint-mf-interaction.qm
    - ctint-mf-interaction.recording
    - ctint-mf-interaction.stt
  ctint-mf-report:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/mf-report
    record-filter:
    - labelEn: Report Type
      labelCh: 報告類型
      value: reportMasterType
      filterType: input
      active: true
      isMetaData: false
    - labelEn: Report Name
      labelCh: 報告名稱
      value: reportMasterName
      filterType: input
      active: true
      isMetaData: false
    - labelEn: User Name
      labelCh: 用戶名稱
      value: userName
      filterType: input
      active: true
      isMetaData: false
    - labelEn: Workgroup
      labelCh: 工作組
      value: workgroup
      filterType: input
      active: true
      isMetaData: false
    - labelEn: Start Time
      labelCh: 開始時間
      value: createTime
      filterType: date
      active: true
      isMetaData: false
    - labelEn: End Time
      labelCh: 結束時間
      value: reportEndTime
      filterType: date
      active: true
      isMetaData: false
    - labelEn: Queue Name
      labelCh: 隊列名稱
      value: queueName
      filterType: input
      active: true
      isMetaData: false
    - labelEn: State
      labelCh: 狀態
      value: state
      filterType: input
      active: true
      isMetaData: false
languages:
  supportedLanguages:
  - en
  - zh-HK
  defaultLanguage: zh-HK
settings:
  defaultStorage: aws
aws:
  default:
    bucket: ctint-cpp-recording-bucket
    region: ap-east-1
    accessKeyIdVariableName: AWS_ACCESS_KEY_ID
    secretAccessKeyVariableName: AWS_SECRET_ACCESS_KEY
permissionRelease: false
