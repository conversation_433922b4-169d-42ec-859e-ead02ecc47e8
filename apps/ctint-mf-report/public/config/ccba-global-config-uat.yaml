auth:
  headers:
  - name: traceId
  - name: tenant
  - name: sourceId
  - name: previousId
  - name: cdss_authorization
logger:
  headers:
  - name: traceId
  - name: tenant
  - name: sourceId
  - name: previousId
pureengage:
  host: http://*************:8090
  recordsLimit: 200
genesys-cloud:
  environment: mypurecloud.com.au
  apiHost: https://api.mypurecloud.com.au
  authHost: https://login.mypurecloud.com.au
loginTotal: 1
missingTotal: 3
poolTotal: 10000
heartbeatTime: 30
socketType: CDSS
recording:
  mediaSource:
  - aws
ctint-dab:
  auth:
    host: http://ctint-dab-auth-service.cdss-data-ctint.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /graphql
    active: true
  config:
    host: http://ctint-dab-config-service.cdss-data-ctint.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /graphql
    active: true
  conv:
    host: http://ctint-dab-conv-service.cdss-data-ctint.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath:
    active: true
  user:
    host: http://ctint-dab-user-service.cdss-data-ctint.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /graphql
    active: true
  dataSync:
    host: http://ctint-dab-datasync-service.cdss-data-ctint.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /graphql
    active: true  
  callControl:
    host: http://ctint-dab-call-control-service.cdss-data-ctint.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    restBasepath: /api
    graphBasepath: /graphql
    active: true
  jobWorker:
    host: http://ctint-dab-job-service.cdss-data-ctint.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    restBasepath: /api
    graphBasepath: /graphql
    active: true
  common:
    host: http://ctint-graphql-service.cdss-data-ctint.svc.cluster.local:8110 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /ctint/query
    active: true
  auditLog:
    host: http://ctint-dab-audit-log-service.cdss-data-ctint.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /graphql
    active: true
  qm:
    host: http://ctint-dab-qm-service.cdss-data-ctint.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /graphql
    active: true
ctint-state:
  auth:
    host: http://ctint-state-auth-service.cdss-data-ctint.svc.cluster.local:35000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /v1.0/state/ctint-state-auth
    active: true
  cdssmf:
    host: http://ctint-state-cdssmf-service.cdss-data-ctint.svc.cluster.local:35010
    basepath: /v1.0/state/ctint-state-cdssmf
    active: true
  session-manager:
    host: http://ctint-state-session-manager-service.cdss-data-ctint.svc.cluster.local:35020
    basepath: /v1.0/state/ctint-state-session-manager
    active: true
portals:
- ctint-mf-cpp
services:
  ctint-stt:
    host: http://************
    basepath: /ctint/stt
    active: true
    provider: 
    - astri2.0
    healthcheck: /healthcheck
  ctint-nlp:
    host: http://************
    basepath: /ctint/nlp
    active: true
    provider: 
    - astri
    healthcheck: /healthcheck
  ctint-auth:
    host: http://************
    basepath: /ctint/auth
    active: true
    provider: # pure_engage / genesys_cloud / ctint-dab-auth / ctint-state-auth
    - genesys-cloud
    - ctint-dab-auth
    - ctint-state-auth
    healthcheck: /healthcheck
  ctint-conv:
    host: http://************
    basepath: /ctint/conv
    active: true
    provider: 
    - pureengage
    - ctint-dab-conv
    healthcheck: /healthcheck
  ctint-config:
    host: http://************
    basepath: /ctint/config
    active: true
    provider: 
    - pureengage
    - ctint-dab-config
    healthcheck: /healthcheck
  ctint-session:
    host: http://************
    basepath: /ctint/session
    active: true
    provider: 
    - ctint-state-cdssmf
    healthcheck: /healthcheck
  ctint-session-manager:
    host: http://************
    basepath: /ctint/session-manager
    active: true
    healthcheck: /healthcheck
  ctint-cdss-ws:
    host: http://************
    basepath: /ctint-cdss-ws
    active: true
    healthcheck: /healthcheck
  ctint-ccsp-ws:
    host: http://************
    basepath: /ctint/ccsp-ws
    active: true
    healthcheck: /healthcheck
  ctint-call-control:
    host: http://************
    basepath: /ctint/call-control
    active: true
    healthcheck: /healthcheck
  ctint-user:
    host: http://************
    basepath: /ctint/user
    active: true
    healthcheck: /healthcheck
  ctint-datasync-hub:
    host: http://************
    basepath: /ctint/datasync-hub
    active: true
    healthcheck: /healthcheck
  ctint-job-worker:
    host: http://************
    basepath: /ctint/job-worker
    active: true
    healthcheck: /healthcheck
  ctint-job-worker-nlp:
    host: http://************
    basepath: /ctint/job-worker-nlp
    active: true
    healthcheck: /healthcheck
  ctint-job-engine:
    host: http://************
    basepath: /ctint/job-engine
    active: true
    healthcheck: /healthcheck
  ctint-qm:
    host: http://************
    basepath: /ctint/qm
    active: true
    provider:
    healthcheck: /healthcheck
microfrontends:
  ctint-mf-cdss:
    host: http://************
    basepath: /ctint/mf-cdss
  ctint-mf-interaction:
    host: http://************
    basepath: /ctint/mf-interaction
languages:
  supportedLanguages:
  - en
  - zh-HK
  defaultLanguage: zh-HK
settings:
  defaultStorage: aws
aws:
  default:
    bucket: ctint-cpp-recording-bucket
    region: ap-east-1
    accessKeyIdVariableName: AWS_ACCESS_KEY_ID
    secretAccessKeyVariableName: AWS_SECRET_ACCESS_KEY