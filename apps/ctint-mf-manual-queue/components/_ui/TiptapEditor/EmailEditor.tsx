import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import { EditorContent, Extension, useEditor } from '@tiptap/react';
import { PasteRule } from '@tiptap/core';
import StarterKit from '@tiptap/starter-kit';
import Underline from '@tiptap/extension-underline';
import TextAlign from '@tiptap/extension-text-align';
import Image from '@tiptap/extension-image';
import Link from '@tiptap/extension-link';
import TextStyle from '@tiptap/extension-text-style';
import Table from '@tiptap/extension-table';
import TableRow from '@tiptap/extension-table-row';
import TableCell from '@tiptap/extension-table-cell';
import TableHeader from '@tiptap/extension-table-header';
import Placeholder from '@tiptap/extension-placeholder';
import Subscript from '@tiptap/extension-subscript';
import Superscript from '@tiptap/extension-superscript';
import { Color } from '@tiptap/extension-color';
import OrderedList from '@tiptap/extension-ordered-list';
import BulletList from '@tiptap/extension-bullet-list';
import ListItem from '@tiptap/extension-list-item';
import { TiptapToolbar } from './index';

// 为自定义命令声明类型，解决TS2353错误
declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    fontSize: {
      /**
       * 设置字体大小
       */
      setFontSize: (fontSize: string) => ReturnType;
      /**
       * 取消字体大小设置
       */
      unsetFontSize: () => ReturnType;
    };
  }
}

// 自定义的FontSize扩展
const FontSize = Extension.create({
  name: 'fontSize',

  addOptions() {
    return {
      types: ['textStyle'],
    };
  },

  addGlobalAttributes() {
    return [
      {
        types: this.options.types,
        attributes: {
          fontSize: {
            default: null,
            parseHTML: (element) => element.style.fontSize,
            renderHTML: (attributes) => {
              if (!attributes.fontSize) {
                return {};
              }

              return {
                style: `font-size: ${attributes.fontSize}`,
              };
            },
          },
        },
      },
    ];
  },

  addCommands() {
    return {
      setFontSize:
        (fontSize: string) =>
        ({ chain }) => {
          return chain().focus().setMark('textStyle', { fontSize }).run();
        },
      unsetFontSize:
        () =>
        ({ chain }) => {
          return chain()
            .focus()
            .setMark('textStyle', { fontSize: null })
            .removeEmptyTextStyle()
            .run();
        },
    };
  },
});

// 扩展组件的引用类型，以便在父组件访问编辑器实例
export interface EmailEditorRef {
  insertImage: (
    src: string,
    title?: string,
    alt?: string,
    width?: number,
    height?: number
  ) => void;
  setContent: (content: string) => void;
  getContent: () => string;
  isEmpty: () => boolean;
  insertContentAtCursor: (content: string) => void;
}

// 为自定义命令声明类型，解决背景色相关的类型问题
declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    // 文本背景色命令
    backgroundColor: {
      /**
       * 设置文本背景色
       */
      setBackgroundColor: (color: string) => ReturnType;
      /**
       * 取消文本背景色设置
       */
      unsetBackgroundColor: () => ReturnType;
    };

    // 表格背景色命令
    tableBackground: {
      /**
       * 设置表格背景色
       */
      setTableBackground: (color: string) => ReturnType;
      /**
       * 设置表格单元格背景色
       */
      setTableCellBackground: (color: string) => ReturnType;
    };
  }
}

const CustomTable = Table.extend({
  name: 'table',

  addAttributes() {
    return {
      ...this.parent?.(),
      // 背景色支持
      backgroundColor: {
        default: null,
        parseHTML: (element) => {
          return (
            element.getAttribute('data-original-bgcolor') ||
            element.getAttribute('bgcolor') ||
            (element.style && element.style.backgroundColor) ||
            null
          );
        },
        renderHTML: (attributes) => {
          if (!attributes.backgroundColor) {
            return {};
          }

          return {
            style: `background-color: ${attributes.backgroundColor} !important`,
            bgcolor: attributes.backgroundColor,
            'data-original-bgcolor': attributes.backgroundColor,
            class: 'has-bgcolor',
          };
        },
      },
      // 宽度支持
      width: {
        default: null,
        parseHTML: (element) => {
          return (
            element.getAttribute('width') ||
            (element.style && element.style.width) ||
            null
          );
        },
        renderHTML: (attributes) => {
          if (!attributes.width) {
            return {};
          }

          return {
            style: `width: ${attributes.width}`,
            width: attributes.width,
            'data-original-width': attributes.width,
            class: 'has-width',
          };
        },
      },
      // 边框支持
      border: {
        default: null,
        parseHTML: (element) => {
          return element.getAttribute('border') || null;
        },
        renderHTML: (attributes) => {
          if (!attributes.border) {
            return {};
          }

          return {
            border: attributes.border,
            'data-original-border': attributes.border,
            class: 'has-border',
          };
        },
      },
      // 边框颜色支持
      borderColor: {
        default: null,
        parseHTML: (element) => {
          return element.getAttribute('bordercolor') || null;
        },
        renderHTML: (attributes) => {
          if (!attributes.borderColor) {
            return {};
          }

          return {
            style: `border-color: ${attributes.borderColor}`,
            bordercolor: attributes.borderColor,
            'data-original-bordercolor': attributes.borderColor,
            class: 'has-bordercolor',
          };
        },
      },
      // 单元格内边距支持
      cellPadding: {
        default: null,
        parseHTML: (element) => {
          return element.getAttribute('cellpadding') || null;
        },
        renderHTML: (attributes) => {
          if (!attributes.cellPadding) {
            return {};
          }

          return {
            cellpadding: attributes.cellPadding,
            'data-original-cellpadding': attributes.cellPadding,
            class: 'has-cellpadding',
          };
        },
      },
      // 单元格间距支持
      cellSpacing: {
        default: null,
        parseHTML: (element) => {
          return element.getAttribute('cellspacing') || null;
        },
        renderHTML: (attributes) => {
          if (!attributes.cellSpacing) {
            return {};
          }

          const style =
            attributes.cellSpacing === '0'
              ? `border-collapse: collapse; border-spacing: 0`
              : `border-collapse: separate; border-spacing: ${attributes.cellSpacing}px`;

          return {
            style,
            cellspacing: attributes.cellSpacing,
            'data-original-cellspacing': attributes.cellSpacing,
            class: 'has-cellspacing',
          };
        },
      },
      // 对齐方式支持
      align: {
        default: null,
        parseHTML: (element) => {
          return element.getAttribute('align') || null;
        },
        renderHTML: (attributes) => {
          if (!attributes.align) {
            return {};
          }

          return {
            style: `text-align: ${attributes.align}`,
            align: attributes.align,
            'data-original-align': attributes.align,
            class: 'has-align',
          };
        },
      },
    };
  },
});

const CustomTableCell = TableCell.extend({
  name: 'tableCell',

  addAttributes() {
    return {
      ...this.parent?.(),
      // 背景色支持
      backgroundColor: {
        default: null,
        parseHTML: (element) => {
          return (
            element.getAttribute('data-original-bgcolor') ||
            element.getAttribute('bgcolor') ||
            (element.style && element.style.backgroundColor) ||
            null
          );
        },
        renderHTML: (attributes) => {
          if (!attributes.backgroundColor) {
            return {};
          }

          return {
            style: `background-color: ${attributes.backgroundColor} !important`,
            bgcolor: attributes.backgroundColor,
            'data-original-bgcolor': attributes.backgroundColor,
            class: 'has-bgcolor',
          };
        },
      },
      // 宽度支持
      width: {
        default: null,
        parseHTML: (element) => {
          return (
            element.getAttribute('width') ||
            (element.style && element.style.width) ||
            null
          );
        },
        renderHTML: (attributes) => {
          if (!attributes.width) {
            return {};
          }

          return {
            style: `width: ${attributes.width}`,
            width: attributes.width,
            'data-original-width': attributes.width,
            class: 'has-width',
          };
        },
      },
      // 对齐方式支持
      align: {
        default: null,
        parseHTML: (element) => {
          return element.getAttribute('align') || null;
        },
        renderHTML: (attributes) => {
          if (!attributes.align) {
            return {};
          }

          return {
            style: `text-align: ${attributes.align}`,
            align: attributes.align,
            'data-original-align': attributes.align,
            class: 'has-align',
          };
        },
      },
      // 垂直对齐支持
      valign: {
        default: null,
        parseHTML: (element) => {
          return element.getAttribute('valign') || null;
        },
        renderHTML: (attributes) => {
          if (!attributes.valign) {
            return {};
          }

          return {
            style: `vertical-align: ${attributes.valign}`,
            valign: attributes.valign,
            'data-original-valign': attributes.valign,
            class: 'has-valign',
          };
        },
      },
    };
  },
});

// 文本背景色扩展
const BackgroundColor = Extension.create({
  name: 'backgroundColor',
  addGlobalAttributes() {
    return [
      {
        types: ['textStyle'],
        attributes: {
          backgroundColor: {
            default: null,
            renderHTML: (attributes) => {
              if (!attributes.backgroundColor) return {};

              // 将 RGB 转换为十六进制（Outlook 更好地支持十六进制）
              let hexColor = attributes.backgroundColor;
              if (attributes.backgroundColor.startsWith('rgb')) {
                const rgbMatch = attributes.backgroundColor.match(
                  /rgb\((\d+),\s*(\d+),\s*(\d+)\)/
                );
                if (rgbMatch) {
                  const r = parseInt(rgbMatch[1]);
                  const g = parseInt(rgbMatch[2]);
                  const b = parseInt(rgbMatch[3]);
                  hexColor = `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`;
                }
              }

              // 返回同时包含 style 和 bgcolor 的属性
              return {
                style: `background-color: ${attributes.backgroundColor} !important`,
                'data-bg-color': attributes.backgroundColor,
                bgcolor: hexColor, // 关键：为 Outlook 添加 bgcolor 属性
                class: 'has-bg-color',
              };
            },
            parseHTML: (element: HTMLElement): string | null =>
              element.style.backgroundColor ||
              element.getAttribute('bgcolor') ||
              element.getAttribute('data-bg-color') ||
              null,
          },
        },
      },
      // 为块级元素（如段落、div）添加背景色支持
      {
        types: ['paragraph', 'heading', 'blockquote'],
        attributes: {
          backgroundColor: {
            default: null,
            renderHTML: (attributes) => {
              if (!attributes.backgroundColor) return {};

              // 将 RGB 转换为十六进制
              let hexColor = attributes.backgroundColor;
              if (attributes.backgroundColor.startsWith('rgb')) {
                const rgbMatch = attributes.backgroundColor.match(
                  /rgb\((\d+),\s*(\d+),\s*(\d+)\)/
                );
                if (rgbMatch) {
                  const r = parseInt(rgbMatch[1]);
                  const g = parseInt(rgbMatch[2]);
                  const b = parseInt(rgbMatch[3]);
                  hexColor = `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`;
                }
              }

              return {
                style: `background-color: ${attributes.backgroundColor} !important`,
                bgcolor: hexColor,
                'data-bg-color': attributes.backgroundColor,
                class: 'has-bg-color',
              };
            },
            parseHTML: (element: HTMLElement): string | null =>
              element.style.backgroundColor ||
              element.getAttribute('bgcolor') ||
              element.getAttribute('data-bg-color') ||
              null,
          },
        },
      },
    ];
  },

  addCommands() {
    return {
      setBackgroundColor:
        (color: string) =>
        ({ chain, state }) => {
          const { from, to, empty } = state.selection;

          if (empty) {
            // 如果没有选中文本，直接设置背景色
            return chain()
              .focus()
              .setMark('textStyle', { backgroundColor: color })
              .run();
          } else {
            // 对于选中的文本，强制包装在 span 标签中
            return chain()
              .focus()
              .setMark('textStyle', { backgroundColor: color })
              .run();
          }
        },
      unsetBackgroundColor:
        () =>
        ({ chain }) => {
          return chain()
            .focus()
            .setMark('textStyle', { backgroundColor: null })
            .removeEmptyTextStyle()
            .run();
        },
    };
  },
});

// 表格背景色扩展
const TableBackgroundExtension = Extension.create({
  name: 'tableBackground',

  addGlobalAttributes() {
    return [
      {
        types: ['table', 'tableCell', 'tableHeader', 'tableRow'],
        attributes: {
          backgroundColor: {
            default: null,
            parseHTML: (element: HTMLElement): string | null => {
              return (
                element.getAttribute('data-original-bgcolor') ||
                element.getAttribute('bgcolor') ||
                (element.style && element.style.backgroundColor) ||
                null
              );
            },
            renderHTML: (
              attributes: Record<string, any>
            ): Record<string, any> => {
              if (!attributes.backgroundColor) {
                return {};
              }

              return {
                style: `background-color: ${attributes.backgroundColor} !important`,
                'data-original-bgcolor': attributes.backgroundColor,
                bgcolor: attributes.backgroundColor,
                class: 'has-bgcolor',
              };
            },
          },
        },
      },
    ];
  },

  // 添加命令以设置表格背景色 - 修复类型问题
  addCommands() {
    return {
      setTableBackground:
        (color: string) =>
        ({ chain }) => {
          return chain()
            .updateAttributes('table', {
              backgroundColor: color,
            })
            .run() as any; // 添加类型断言
        },
      setTableCellBackground:
        (color: string) =>
        ({ chain }) => {
          return chain()
            .updateAttributes('tableCell', {
              backgroundColor: color,
            })
            .run() as any; // 添加类型断言
        },
    };
  },
});

// 处理MS邮件内容的扩展
const MsEmailPaste = Extension.create({
  name: 'msEmailPaste',
  addPasteRules() {
    return [
      {
        type: 'htmlToDoc',
        priority: 100,
        handler: (
          props: { html: string },
          next: (props: { html: string }) => any
        ) => {
          const { html } = props;
          if (
            html.includes('mso-') ||
            html.includes('urn:schemas-microsoft-com')
          ) {
            props.html = processHtmlForEditor(html);
          }
          return next(props);
        },
        // 添加find属性以满足PasteRule类型要求
        find: () => null,
      } as unknown as PasteRule,
    ];
  },
});

// 自定义扩展Image组件，添加width和height属性支持
const ResizableImage = Image.extend({
  addAttributes() {
    // 首先获取父类的属性
    const parentAttributes = this.parent?.() || {
      src: { default: null },
      alt: { default: null },
      title: { default: null },
    };

    // 添加width和height属性
    return {
      ...parentAttributes,
      width: {
        default: null,
        // 从DOM元素解析width属性
        parseHTML: (element) => element.getAttribute('width'),
        // 渲染width属性到HTML
        renderHTML: (attributes) => {
          if (!attributes.width) {
            return {};
          }
          return { width: attributes.width };
        },
      },
      height: {
        default: null,
        parseHTML: (element) => element.getAttribute('height'),
        renderHTML: (attributes) => {
          if (!attributes.height) {
            return {};
          }
          return { height: attributes.height };
        },
      },
      // 可选：添加style属性支持，确保内联样式也能被保留
      style: {
        default: null,
        parseHTML: (element) => element.getAttribute('style'),
        renderHTML: (attributes) => {
          if (!attributes.style) {
            return {};
          }
          return { style: attributes.style };
        },
      },
    };
  },
}).configure({
  inline: true,
  allowBase64: true,
  HTMLAttributes: {
    class: 'resizable-image',
  },
});

/**
 * 全面处理HTML内容，特别是针对邮件中的复杂表格样式
 * @param html 原始HTML内容
 * @returns 处理后的HTML内容
 */
export const processHtmlForEditor = (html: string): string => {
  if (!html) return '';

  // 使用DOMParser解析HTML
  const parser = new DOMParser();
  const doc = parser.parseFromString(html, 'text/html');

  // 处理所有表格元素（table, tr, td, th）
  const tableElements = doc.querySelectorAll('table, tr, td, th');
  tableElements.forEach((el) => {
    // 1. 处理bgcolor属性
    const bgcolor = el.getAttribute('bgcolor');
    if (bgcolor) {
      // 保存原始bgcolor值到自定义数据属性
      el.setAttribute('data-original-bgcolor', bgcolor);
      // 设置内联样式，使用!important确保优先级
      (el as HTMLElement).style.cssText +=
        `; background-color: ${bgcolor} !important;`;
      // 添加特殊标记类
      el.classList.add('has-bgcolor');
    }

    // 2. 处理宽度属性
    const width = el.getAttribute('width');
    if (width) {
      // 保存原始width值到自定义数据属性
      el.setAttribute('data-original-width', width);
      // 设置内联样式
      let widthValue = width;
      // 如果宽度是纯数字，添加px单位
      if (/^\d+$/.test(width)) {
        widthValue = `${width}px`;
      }
      (el as HTMLElement).style.cssText += `; width: ${widthValue} !important;`;
      el.classList.add('has-width');
    }

    // 3. 处理边框属性
    const border = el.getAttribute('border');
    if (border) {
      // 保存原始border值到自定义数据属性
      el.setAttribute('data-original-border', border);
      // 设置内联样式
      const borderWidth =
        border === '0' ? '0px' : parseInt(border) > 0 ? `${border}px` : '1px';
      (el as HTMLElement).style.cssText +=
        `; border-width: ${borderWidth} !important;`;
      if (border !== '0') {
        (el as HTMLElement).style.cssText +=
          `; border-style: solid !important;`;
      }
      el.classList.add('has-border');
    }

    // 4. 处理bordercolor属性
    const bordercolor = el.getAttribute('bordercolor');
    if (bordercolor) {
      // 保存原始bordercolor值到自定义数据属性
      el.setAttribute('data-original-bordercolor', bordercolor);
      // 设置内联样式
      (el as HTMLElement).style.cssText +=
        `; border-color: ${bordercolor} !important;`;
      el.classList.add('has-bordercolor');
    }

    // 5. 处理cellpadding属性
    const cellpadding = el.getAttribute('cellpadding');
    if (cellpadding && el.tagName.toLowerCase() === 'table') {
      // 保存原始cellpadding值到自定义数据属性
      el.setAttribute('data-original-cellpadding', cellpadding);
      // 为表格内的所有单元格添加内边距
      const cells = el.querySelectorAll('td, th');
      cells.forEach((cell) => {
        (cell as HTMLElement).style.cssText +=
          `; padding: ${cellpadding}px !important;`;
      });
      el.classList.add('has-cellpadding');
    }

    // 6. 处理cellspacing属性
    const cellspacing = el.getAttribute('cellspacing');
    if (cellspacing && el.tagName.toLowerCase() === 'table') {
      // 保存原始cellspacing值到自定义数据属性
      el.setAttribute('data-original-cellspacing', cellspacing);
      // 处理表格间距 - 这需要更复杂的CSS，但可以通过border-spacing属性实现
      (el as HTMLElement).style.cssText +=
        `; border-spacing: ${cellspacing}px !important;`;
      if (cellspacing === '0') {
        (el as HTMLElement).style.cssText +=
          `; border-collapse: collapse !important;`;
      } else {
        (el as HTMLElement).style.cssText +=
          `; border-collapse: separate !important;`;
      }
      el.classList.add('has-cellspacing');
    }

    // 7. 处理align属性
    const align = el.getAttribute('align');
    if (align) {
      // 保存原始align值到自定义数据属性
      el.setAttribute('data-original-align', align);
      // 设置内联样式
      (el as HTMLElement).style.cssText += `; text-align: ${align} !important;`;
      el.classList.add('has-align');
    }

    // 8. 处理valign属性
    const valign = el.getAttribute('valign');
    if (valign) {
      // 保存原始valign值到自定义数据属性
      el.setAttribute('data-original-valign', valign);
      // 设置内联样式
      (el as HTMLElement).style.cssText +=
        `; vertical-align: ${valign} !important;`;
      el.classList.add('has-valign');
    }

    // 9. 处理style中已有的CSS属性
    if ((el as HTMLElement).style) {
      // 背景色
      if ((el as HTMLElement).style.backgroundColor) {
        const bgColor = (el as HTMLElement).style.backgroundColor;
        (el as HTMLElement).style.setProperty(
          'background-color',
          bgColor,
          'important'
        );
        el.setAttribute('data-bg-color', bgColor);
        el.classList.add('has-bg-style');
      }

      // 边框样式
      if (
        (el as HTMLElement).style.borderWidth ||
        (el as HTMLElement).style.borderStyle ||
        (el as HTMLElement).style.borderColor
      ) {
        el.classList.add('has-border-style');
      }

      // 宽度
      if ((el as HTMLElement).style.width) {
        const widthValue = (el as HTMLElement).style.width;
        (el as HTMLElement).style.setProperty('width', widthValue, 'important');
        el.classList.add('has-width-style');
      }
    }
  });

  // 特殊处理Teams邮件
  if (
    html.includes('teams.microsoft.com') ||
    html.includes('style="background:revert') ||
    html.includes('microsoft.com')
  ) {
    // 查找Teams特定表格
    const teamsSpecificTables = doc.querySelectorAll(
      'table[style*="revert"], table[style*="table-layout:fixed"], table[align="left"]'
    );
    teamsSpecificTables.forEach((table) => {
      // 添加特殊标记类
      table.classList.add('teams-table');

      // 确保表格布局保持固定
      (table as HTMLElement).style.tableLayout = 'fixed';

      // 处理背景色单元格
      const cells = table.querySelectorAll(
        'td[bgcolor], td[style*="background-color"]'
      );
      cells.forEach((cell) => {
        const bgcolor = cell.getAttribute('bgcolor');
        if (bgcolor) {
          (cell as HTMLElement).style.backgroundColor = bgcolor;
          cell.setAttribute(
            'style',
            `${(cell as HTMLElement).getAttribute('style') || ''}; background-color: ${bgcolor} !important;`
          );
          cell.classList.add('teams-bg-cell');
        }

        // 处理内联背景色
        if (
          (cell as HTMLElement).style &&
          (cell as HTMLElement).style.backgroundColor
        ) {
          const bgColor = (cell as HTMLElement).style.backgroundColor;
          (cell as HTMLElement).style.setProperty(
            'background-color',
            bgColor,
            'important'
          );
          cell.classList.add('teams-bg-cell');
        }
      });
    });

    // 处理特殊边框
    const borderedElements = doc.querySelectorAll('[style*="border"]');
    borderedElements.forEach((el) => {
      el.classList.add('has-custom-border');
    });
  }

  // 添加内联样式表确保所有表格样式正确显示
  const styleEl = doc.createElement('style');
  styleEl.textContent = `
    /* 表格基本样式重置 */
    table.email-table {
      border-collapse: separate !important;
      border-spacing: inherit !important;
      width: auto !important;
      table-layout: auto !important;
    }

    /* 强制应用背景色 */
    [data-original-bgcolor] {
      background-color: attr(data-original-bgcolor) !important;
    }

    [bgcolor] {
      background-color: attr(bgcolor) !important;
    }

    .has-bgcolor {
      background-color: inherit !important;
    }

    /* 强制应用宽度 */
    [data-original-width] {
      width: attr(data-original-width) !important;
    }

    .has-width {
      width: inherit !important;
    }

    /* 强制应用边框样式 */
    [data-original-border] {
      border-width: attr(data-original-border) !important;
    }

    [data-original-bordercolor] {
      border-color: attr(data-original-bordercolor) !important;
    }

    .has-border {
      border-style: inherit !important;
      border-width: inherit !important;
    }

    .has-bordercolor {
      border-color: inherit !important;
    }

    /* 对齐方式 */
    [data-original-align] {
      text-align: attr(data-original-align) !important;
    }

    [data-original-valign] {
      vertical-align: attr(data-original-valign) !important;
    }

    /* Teams特殊表格 */
    .teams-table {
      border-collapse: separate !important;
      border-spacing: 0 !important;
      table-layout: fixed !important;
    }

    .teams-bg-cell {
      background-color: inherit !important;
    }

    /* 常见的Teams邮件背景色 */
    .teams-bg-cell[bgcolor="#A6A6A6"] {
      background-color: #A6A6A6 !important;
    }

    .teams-bg-cell[bgcolor="#EAEAEA"] {
      background-color: #EAEAEA !important;
    }

    .teams-bg-cell[bgcolor="#f7f7f7"] {
      background-color: #f7f7f7 !important;
    }

    .teams-bg-cell[bgcolor="#f8f8f8"] {
      background-color: #f8f8f8 !important;
    }

    /* 处理Microsoft Teams中的按钮样式 */
    [bgcolor="#6264a7"] {
      background-color: #6264a7 !important;
    }

    /* 确保内联样式优先 */
    [style*="background-color"] {
      background-color: inherit !important;
    }

    [style*="border"] {
      border: inherit !important;
    }

    [style*="width"] {
      width: inherit !important;
    }

    [style*="text-align"] {
      text-align: inherit !important;
    }

    [style*="vertical-align"] {
      vertical-align: inherit !important;
    }
  `;

  // 将样式添加到文档头部
  const headEl = doc.head || doc.getElementsByTagName('head')[0];
  if (headEl) {
    headEl.appendChild(styleEl);
  }

  // 标记所有表格
  doc.querySelectorAll('table').forEach((table) => {
    table.classList.add('email-table');
  });

  // 添加特殊标记类到文档
  doc.documentElement.classList.add('email-content');

  return doc.documentElement.outerHTML;
};

/**
 * 处理空格显示问题 - 在发送邮件前调用
 * @param html 原始HTML内容
 * @returns 处理后的HTML内容
 */
export const processHtmlForEmailSend = (html: string): string => {
  if (!html) return '';

  let processedHtml = html;

  // 1. 处理空段落（真正的空行）- 这些需要显示为空行
  processedHtml = processedHtml.replace(
    /<p[^>]*>\s*<\/p>/g,
    '<p style="margin: 0; padding: 0; line-height: 1.2; min-height: 1.2em;">&nbsp;</p>'
  );

  // 2. 处理只包含 &nbsp; 的段落
  processedHtml = processedHtml.replace(
    /<p[^>]*>(\s*&nbsp;\s*)+<\/p>/g,
    '<p style="margin: 0; padding: 0; line-height: 1.2; min-height: 1.2em;">&nbsp;</p>'
  );

  // 3. 为普通段落添加样式 - 减小间距
  processedHtml = processedHtml.replace(
    /<p([^>]*?)>/g,
    '<p$1 style="margin: 0 0 0.3em 0; padding: 0; line-height: 1.2;">'
  );

  // 4. 处理连续空格（保持原有逻辑）
  processedHtml = processedHtml
    .replace(/&nbsp;/g, '___NBSP_PLACEHOLDER___')
    .replace(/  +/g, (match) => {
      return ' ' + '&nbsp;'.repeat(match.length - 1);
    })
    .replace(/___NBSP_PLACEHOLDER___/g, '&nbsp;');

  return processedHtml;
};

interface EmailEditorProps {
  initialContent?: string;
  onContentChange?: (html: string) => void;
  placeholder?: string;
  onAddLink?: () => void;
  onAddImage?: () => void;
}

// 自定义的Email编辑器组件，使用forwardRef传递引用
const EmailEditor = forwardRef<EmailEditorRef, EmailEditorProps>(
  (
    {
      initialContent = '',
      onContentChange,
      placeholder = '撰写您的邮件...',
      onAddImage,
    },
    ref
  ) => {
    // 自定义CSS样式
    const emailEditorStyles = `
/* 编辑器内容基本样式 */
.ProseMirror {
  min-height: 150px;
  outline: none;
  /* 修改：设置正常的行距 */
  line-height: 1.2 !important;
  font-size: 14px !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif !important;
}

/* 链接样式 */
.ProseMirror a.email-editor-link {
  color: #0066cc !important;
  text-decoration: underline !important;
  cursor: pointer !important;
}

.ProseMirror a.email-editor-link:hover {
  color: #0056b3 !important;
}

/* 图片样式 - 确保维持原始尺寸 */
.ProseMirror img {
  max-width: 100%;
  height: auto;
  cursor: default;
  display: inline-block;
  position: relative; /* 确保可以进行绝对定位 */
}

/* 被选中图片的样式 */
.ProseMirror img.ProseMirror-selectednode {
  outline: 3px solid #68CEF8;
  border-radius: 2px;
}

/* 可调整大小的图片样式 */
.ProseMirror .resizable-image {
  position: relative;
  display: inline-block;
  margin: 0;
}

.ProseMirror .resizable-image img {
  display: block;
}

/* 图片右下角调整大小的控制柄 */
.ProseMirror img::after {
  content: '';
  position: absolute;
  bottom: 0;
  right: 0;
  width: 15px;
  height: 15px;
  cursor: nwse-resize; /* 对角线调整大小的鼠标指针 */
  opacity: 0; /* 默认隐藏 */
  transition: opacity 0.2s ease, background-color 0.2s ease;
}

/* 鼠标悬停时显示调整大小的控制柄 */
.ProseMirror img:hover::after {
  opacity: 1;
  background-color: rgba(104, 206, 248, 0.6);
  border-radius: 50%;
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);
}

/* 为调整大小创建更大的点击区域 */
.ProseMirror img::before {
  content: '';
  position: absolute;
  bottom: 0;
  right: 0;
  width: 20px;
  height: 20px;
  cursor: nwse-resize;
}

/* 被选中图片的增强型调整大小控制柄 */
.ProseMirror .resizable-image.ProseMirror-selectednode::after {
  content: '';
  position: absolute;
  bottom: -6px;
  right: -6px;
  width: 12px;
  height: 12px;
  background-color: #68CEF8;
  border: 2px solid white;
  border-radius: 50%;
  cursor: nwse-resize;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.3);
  opacity: 1;
}

/* 正在调整大小时的全局鼠标样式 */
body.resizing-image,
body.resizing-image * {
  cursor: nwse-resize !important;
}

/* 完整的邮件表格样式处理 */
/* 表格基本样式 */
.ProseMirror table {
  border-collapse: separate;
  margin: 0;
  overflow: hidden;
  table-layout: auto;
  width: auto;
}

.ProseMirror .email-table {
  border-collapse: inherit !important;
  border-spacing: inherit !important;
  width: inherit !important;
  table-layout: inherit !important;
}

/* 表格单元格基本样式 */
.ProseMirror td, .ProseMirror th {
  border: 1px solid #ddd;
  box-sizing: border-box;
  min-width: 1em;
  padding: 3px 5px;
  position: relative;
  vertical-align: top;
}

/* 背景色处理 */
.ProseMirror table[bgcolor],
.ProseMirror tr[bgcolor],
.ProseMirror td[bgcolor],
.ProseMirror th[bgcolor] {
  background-color: var(--table-bg-color, inherit) !important;
}

.ProseMirror [data-original-bgcolor] {
  background-color: var(--original-bgcolor, inherit) !important;
}

/* 宽度处理 */
.ProseMirror table[width],
.ProseMirror td[width],
.ProseMirror th[width] {
  width: var(--table-width, inherit) !important;
}

/* 边框处理 */
.ProseMirror table[border],
.ProseMirror td[border],
.ProseMirror th[border] {
  border-width: var(--border-width, 1px) !important;
  border-style: solid !important;
}

.ProseMirror table[bordercolor],
.ProseMirror td[bordercolor],
.ProseMirror th[bordercolor] {
  border-color: var(--border-color, #ddd) !important;
}

/* 对齐方式处理 */
.ProseMirror table[align],
.ProseMirror td[align],
.ProseMirror th[align] {
  text-align: var(--text-align, left) !important;
}

.ProseMirror table[valign],
.ProseMirror td[valign],
.ProseMirror th[valign] {
  vertical-align: var(--vert-align, top) !important;
}

/* Teams邮件特殊处理 */
.ProseMirror .teams-table {
  border-collapse: separate !important;
  border-spacing: 0 !important;
  table-layout: fixed !important;
  width: 100% !important;
}

.ProseMirror .teams-bg-cell {
  background-color: inherit !important;
}

/* 常见的Teams邮件背景色硬编码 */
.ProseMirror .teams-bg-cell[bgcolor="#A6A6A6"],
.ProseMirror td[bgcolor="#A6A6A6"],
.ProseMirror th[bgcolor="#A6A6A6"] {
  background-color: #A6A6A6 !important;
}

.ProseMirror .teams-bg-cell[bgcolor="#EAEAEA"],
.ProseMirror td[bgcolor="#EAEAEA"],
.ProseMirror th[bgcolor="#EAEAEA"] {
  background-color: #EAEAEA !important;
}

.ProseMirror .teams-bg-cell[bgcolor="#f7f7f7"],
.ProseMirror td[bgcolor="#f7f7f7"],
.ProseMirror th[bgcolor="#f7f7f7"] {
  background-color: #f7f7f7 !important;
}

.ProseMirror .teams-bg-cell[bgcolor="#f8f8f8"],
.ProseMirror td[bgcolor="#f8f8f8"],
.ProseMirror th[bgcolor="#f8f8f8"] {
  background-color: #f8f8f8 !important;
}

/* Teams按钮处理 */
.ProseMirror td[bgcolor="#6264a7"],
.ProseMirror table[bgcolor="#6264a7"] {
  background-color: #6264a7 !important;
}

/* 类属性选择器处理 */
.ProseMirror .has-bgcolor {
  background-color: inherit !important;
}

.ProseMirror .has-width {
  width: inherit !important;
}

.ProseMirror .has-border {
  border-width: inherit !important;
  border-style: inherit !important;
}

.ProseMirror .has-bordercolor {
  border-color: inherit !important;
}

.ProseMirror .has-align {
  text-align: inherit !important;
}

.ProseMirror .has-valign {
  vertical-align: inherit !important;
}

/* 特殊边框处理 */
.ProseMirror .has-custom-border {
  border: inherit !important;
}

/* 确保内联样式正确显示 */
.ProseMirror [style*="background-color"] {
  background-color: inherit !important;
}

.ProseMirror [style*="border"] {
  border: inherit !important;
}

.ProseMirror [style*="width"] {
  width: inherit !important;
}

.ProseMirror [style*="text-align"] {
  text-align: inherit !important;
}

.ProseMirror [style*="vertical-align"] {
  vertical-align: inherit !important;
}

/* 表格背景色处理 - 原始代码 */
.ProseMirror table[data-original-bgcolor],
.ProseMirror table[bgcolor] {
  background-color: var(--table-bg-color, inherit) !important;
}

.ProseMirror table[bgcolor] {
  --table-bg-color: attr(bgcolor);
}

.ProseMirror table[style*="background-color"] {
  background-color: inherit !important;
}

/* 单元格背景色处理 - 原始代码 */
.ProseMirror td[data-original-bgcolor],
.ProseMirror td[bgcolor],
.ProseMirror th[data-original-bgcolor],
.ProseMirror th[bgcolor] {
  background-color: var(--cell-bg-color, inherit) !important;
}

.ProseMirror td[bgcolor], .ProseMirror th[bgcolor] {
  --cell-bg-color: attr(bgcolor);
}

.ProseMirror td[style*="background-color"],
.ProseMirror th[style*="background-color"] {
  background-color: inherit !important;
}

/* 优化表格结构，避免Tiptap覆盖关键样式 */
.ProseMirror table.custom-table {
  border-collapse: inherit !important;
  border-spacing: inherit !important;
  background-color: inherit !important;
}

/* 确保表格背景色继承 */
.ProseMirror table.custom-table td,
.ProseMirror table.custom-table th {
  background-color: inherit !important;
}

/* 特殊处理有bgcolor数据属性的元素 */
.ProseMirror [data-bgcolor] {
  background-color: attr(data-bgcolor) !important;
}

/* 确保data-original-bgcolor属性正常工作 */
.ProseMirror [data-original-bgcolor] {
  background-color: attr(data-original-bgcolor) !important;
}

/* Microsoft特定样式的处理 */
.ProseMirror [style*="mso-"] {
  font-family: 'Calibri', 'Segoe UI', Arial, sans-serif !important;
}

/* 带有title属性的图片（通常是内联图片）特殊处理 */
.ProseMirror img[title] {
  display: inline-block;
}

/* 列表样式 - 修改行距 */
.ProseMirror ol {
  list-style-type: decimal;
  padding-left: 2em;
  margin: 0 0 0.5em 0 !important; /* 修改：减少间距 */
  line-height: 1.2 !important; /* 修改：正常行距 */
}

.ProseMirror ul {
  list-style-type: disc;
  padding-left: 2em;
  margin: 0 0 0.5em 0 !important; /* 修改：减少间距 */
  line-height: 1.2 !important; /* 修改：正常行距 */
}

.ProseMirror li {
  margin: 0 0 0.2em 0 !important; /* 修改：减少间距 */
  line-height: 1.2 !important; /* 修改：正常行距 */
  font-size: 14px !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif !important;
}

/* 字体大小样式 - 修复字体大小不生效的问题 */
.ProseMirror [style*="font-size"] {
  font-size: unset;
}

/* 确保文本样式生效 */
.ProseMirror [style] {
  color: inherit;
  font-family: inherit;
}

/* 确保textStyle标记不显示背景色 */
.ProseMirror mark[data-type="textStyle"] {
  background-color: transparent;
}

/* 但是当有明确的背景色属性时，使用指定的颜色 */
.ProseMirror mark[data-type="textStyle"][style*="background-color"] {
  background-color: inherit !important;
}

/* 确保textStyle标记中的背景色能够正确显示 */
.ProseMirror mark[data-type="textStyle"][data-original-bgcolor] {
  background-color: attr(data-original-bgcolor) !important;
}

/* 预定义的字体大小类 */
.ProseMirror .text-small {
  font-size: 0.8em;
}

.ProseMirror .text-large {
  font-size: 1.4em;
}

.ProseMirror .text-xl {
  font-size: 1.8em;
}

/* 修复标题样式 - 修改行距和间距 */
.ProseMirror h1 {
  font-size: 2em;
  margin: 0.5em 0 !important; /* 修改：减少间距 */
  font-weight: bold;
  line-height: 1.2 !important; /* 修改：正常行距 */
}

.ProseMirror h2 {
  font-size: 1.5em;
  margin: 0.5em 0 !important; /* 修改：减少间距 */
  font-weight: bold;
  line-height: 1.2 !important; /* 修改：正常行距 */
}

.ProseMirror h3 {
  font-size: 1.17em;
  margin: 0.5em 0 !important; /* 修改：减少间距 */
  font-weight: bold;
  line-height: 1.2 !important; /* 修改：正常行距 */
}

/* 段落样式 - 关键修改 */
.ProseMirror p {
  margin: 0 0 0.5em 0 !important; /* 修改：减少段落间距 */
  padding: 0 !important;
  line-height: 1.2 !important; /* 修改：正常行距 */
  font-size: 14px !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif !important;
}

/* 空段落处理 - 修改 */
.ProseMirror p:empty {
  margin: 0 0 0.5em 0 !important; /* 修改：减少间距 */
  min-height: 1.2em; /* 修改：正常行高 */
}

/* div元素样式 - 修改 */
.ProseMirror div {
  line-height: 1.2 !important; /* 修改：正常行距 */
  font-size: 14px !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif !important;
}

/* 确保带有bgcolor的元素显示背景色 */
.ProseMirror [bgcolor] {
  background-color: attr(bgcolor) !important;
}

/* 确保有宽高属性的图片使用属性值 */
.ProseMirror img[width][height] {
  height: auto;
}

.ProseMirror .email-content {
  --bgcolor-fix-enabled: true;
}

/* 强化表格背景色处理 */
.ProseMirror table,
.ProseMirror td,
.ProseMirror th,
.ProseMirror tr {
  background-color: inherit;
}

/* 强制应用bgcolor属性 */
.ProseMirror table[bgcolor] {
  background-color: var(--bg-color, inherit) !important;
}
.ProseMirror td[bgcolor],
.ProseMirror th[bgcolor] {
  background-color: var(--bg-color, inherit) !important;
}
.ProseMirror tr[bgcolor] {
  background-color: var(--bg-color, inherit) !important;
}

/* 处理data属性背景色 */
.ProseMirror [data-original-bgcolor] {
  background-color: var(--original-bgcolor, inherit) !important;
}

/* 修复Teams邮件特殊样式 */
.ProseMirror [style*="revert"] {
  all: revert;
  background-color: inherit;
}

/* 确保Teams表格正确显示 */
.ProseMirror table[style*="background-color"] {
  background-color: inherit !important;
}

/* 不覆盖带有明确背景色的元素 */
.ProseMirror .has-bgcolor {
  background-color: inherit !important;
}

/* 对于很顽固的表格，使用特殊处理 */
.ProseMirror table[bgcolor="#A6A6A6"],
.ProseMirror td[bgcolor="#A6A6A6"] {
  background-color: #A6A6A6 !important;
}

.ProseMirror table[bgcolor="#EAEAEA"],
.ProseMirror td[bgcolor="#EAEAEA"] {
  background-color: #EAEAEA !important;
}

/* 新增：文本对齐强化规则 */
/* 确保文本对齐样式能够正确应用并覆盖其他样式 */
.ProseMirror p[style*="text-align: left"],
.ProseMirror h1[style*="text-align: left"],
.ProseMirror h2[style*="text-align: left"],
.ProseMirror h3[style*="text-align: left"],
.ProseMirror div[style*="text-align: left"],
.ProseMirror li[style*="text-align: left"],
.ProseMirror blockquote[style*="text-align: left"] {
  text-align: left !important;
}

.ProseMirror p[style*="text-align: center"],
.ProseMirror h1[style*="text-align: center"],
.ProseMirror h2[style*="text-align: center"],
.ProseMirror h3[style*="text-align: center"],
.ProseMirror div[style*="text-align: center"],
.ProseMirror li[style*="text-align: center"],
.ProseMirror blockquote[style*="text-align: center"] {
  text-align: center !important;
}

.ProseMirror p[style*="text-align: right"],
.ProseMirror h1[style*="text-align: right"],
.ProseMirror h2[style*="text-align: right"],
.ProseMirror h3[style*="text-align: right"],
.ProseMirror div[style*="text-align: right"],
.ProseMirror li[style*="text-align: right"],
.ProseMirror blockquote[style*="text-align: right"] {
  text-align: right !important;
}

/* 处理align属性的对齐方式 */
.ProseMirror p[align="left"],
.ProseMirror h1[align="left"],
.ProseMirror h2[align="left"],
.ProseMirror h3[align="left"],
.ProseMirror div[align="left"],
.ProseMirror li[align="left"],
.ProseMirror blockquote[align="left"] {
  text-align: left !important;
}

.ProseMirror p[align="center"],
.ProseMirror h1[align="center"],
.ProseMirror h2[align="center"],
.ProseMirror h3[align="center"],
.ProseMirror div[align="center"],
.ProseMirror li[align="center"],
.ProseMirror blockquote[align="center"] {
  text-align: center !important;
}

.ProseMirror p[align="right"],
.ProseMirror h1[align="right"],
.ProseMirror h2[align="right"],
.ProseMirror h3[align="right"],
.ProseMirror div[align="right"],
.ProseMirror li[align="right"],
.ProseMirror blockquote[align="right"] {
  text-align: right !important;
}

/* 处理包含TextAlign标记的段落 */
.ProseMirror .has-text-align-left {
  text-align: left !important;
}

.ProseMirror .has-text-align-center {
  text-align: center !important;
}

.ProseMirror .has-text-align-right {
  text-align: right !important;
}

/* 覆盖Tiptap默认样式 */
.ProseMirror .is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: #adb5bd;
  pointer-events: none;
  height: 0;
}

/* 确保段落和其他块级元素的对齐方式正确应用 */
.ProseMirror p[data-text-align="left"],
.ProseMirror h1[data-text-align="left"],
.ProseMirror h2[data-text-align="left"],
.ProseMirror h3[data-text-align="left"],
.ProseMirror div[data-text-align="left"],
.ProseMirror blockquote[data-text-align="left"] {
  text-align: left !important;
}

.ProseMirror p[data-text-align="center"],
.ProseMirror h1[data-text-align="center"],
.ProseMirror h2[data-text-align="center"],
.ProseMirror h3[data-text-align="center"],
.ProseMirror div[data-text-align="center"],
.ProseMirror blockquote[data-text-align="center"] {
  text-align: center !important;
}

.ProseMirror p[data-text-align="right"],
.ProseMirror h1[data-text-align="right"],
.ProseMirror h2[data-text-align="right"],
.ProseMirror h3[data-text-align="right"],
.ProseMirror div[data-text-align="right"],
.ProseMirror blockquote[data-text-align="right"] {
  text-align: right !important;
}

/* 新增和修改：文本背景颜色处理 - 修复选中文本设置背景色问题 */
/* 确保背景颜色能够正确应用到所有文本类型 */
.ProseMirror mark[data-type="textStyle"] {
  background-color: transparent; /* 基础状态为透明 */
}

/* 确保带有背景色的textStyle标记正确显示背景色 */
.ProseMirror mark[data-type="textStyle"][style*="background-color"] {
  background-color: inherit !important; /* 使用inherit来保持背景色 */
  padding: 0 1px; /* 添加少量内边距以确保背景色显示完整 */
  -webkit-box-decoration-break: clone; /* 确保跨行元素的背景色正确显示 */
  box-decoration-break: clone;
}

/* 增强处理文本背景色确保可见 */
.ProseMirror .has-bg-color,
.ProseMirror [data-bg-color] {
  background-color: inherit !important;
  padding: 0 1px;
  -webkit-box-decoration-break: clone;
  box-decoration-break: clone;
}

/* 强调显示指定背景色的元素 */
.ProseMirror span[style*="background-color"],
.ProseMirror mark[style*="background-color"] {
  background-color: inherit !important;
  display: inline;
  padding: 0 1px;
  -webkit-box-decoration-break: clone;
  box-decoration-break: clone;
}

/* 确保所有文本容器元素能够正确继承和显示背景色 */
.ProseMirror p,
.ProseMirror h1,
.ProseMirror h2,
.ProseMirror h3,
.ProseMirror li,
.ProseMirror div,
.ProseMirror span,
.ProseMirror a,
.ProseMirror strong,
.ProseMirror em,
.ProseMirror mark {
  background-color: inherit; /* 确保背景色能够正确继承 */
}

/* 防止透明背景色覆盖特定文本元素 */
.ProseMirror mark.text-highlight,
.ProseMirror span.text-highlight {
  background-color: inherit !important;
  padding: 0 1px;
}

/* 确保内联样式优先于编辑器默认样式 */
.ProseMirror *[style*="background-color"] {
  background-color: inherit !important;
}

/* 增强文本背景颜色在选择状态下的显示 */
.ProseMirror .ProseMirror-selectednode mark[data-type="textStyle"],
.ProseMirror .ProseMirror-selectednode span[style*="background-color"] {
  /* 高亮所选节点的背景颜色,确保可见 */
  background-color: inherit !important;
  outline: 1px dashed rgba(0, 0, 0, 0.2);
}
`;

    const [showLinkInput, setShowLinkInput] = useState(false);
    const [linkUrl, setLinkUrl] = useState('');
    const linkInputRef = useRef<HTMLInputElement>(null);
    const editor = useEditor({
      extensions: [
        // 不要使用整个StarterKit，而是分别引入所需扩展
        StarterKit.configure({
          heading: { levels: [1, 2, 3] },
          // 禁用默认的列表，我们将单独引入
          bulletList: false,
          orderedList: false,
          listItem: false,
        }),
        // 显式配置列表扩展
        OrderedList.configure({
          HTMLAttributes: {
            class: 'ordered-list',
          },
        }),
        BulletList.configure({
          HTMLAttributes: {
            class: 'bullet-list',
          },
        }),
        ListItem,
        Underline,
        TextAlign.configure({
          types: [
            'heading',
            'paragraph',
            'blockquote',
            'listItem',
            'tableCell',
            'tableHeader',
          ],
          alignments: ['left', 'center', 'right'],
        }),
        Link.configure({
          openOnClick: false,
          HTMLAttributes: {
            target: '_blank',
            rel: 'noopener noreferrer',
            class: 'email-editor-link',
          },
          validate: (url) => /^https?:\/\//.test(url),
        }),
        ResizableImage.configure({
          inline: false,
          allowBase64: true,
          HTMLAttributes: {
            class: 'resizable-image',
          },
        }),
        TableBackgroundExtension,
        // 使用自定义表格扩展
        CustomTable.configure({
          resizable: true,
          // 允许表格背景色
          HTMLAttributes: {
            class: 'custom-table',
          },
        }),
        TableRow,
        CustomTableCell,
        TableHeader,
        Placeholder.configure({
          placeholder,
        }),
        // 注意顺序：TextStyle需要在FontSize和BackgroundColor之前
        TextStyle,
        // 添加自定义字体大小扩展
        FontSize.configure({
          types: ['textStyle'],
        }),
        Color,
        Subscript,
        Superscript,
        BackgroundColor,
        MsEmailPaste,
      ],
      content: initialContent,
      onUpdate: ({ editor }) => {
        // 自动调整高度
        const editorHeight = editor.view.dom.clientHeight;
        if (editorHeight < 150) {
          editor.view.dom.style.minHeight = '150px';
        }

        // 通知内容变化
        if (onContentChange) {
          onContentChange(editor.getHTML());
        }
      },
      parseOptions: {
        preserveWhitespace: 'full',
      },
      editorProps: {
        transformPastedHTML(html) {
          // 在这里可以预处理 HTML
          return html;
        },
      },
      injectCSS: true,
    });
    const handleAddLink = () => {
      // 显示链接输入界面
      setShowLinkInput(true);
      // 使用setTimeout确保在下一个渲染周期后获取焦点
      setTimeout(() => {
        linkInputRef.current?.focus();
      }, 0);
    };

    // 处理链接提交
    const handleLinkSubmit = () => {
      if (!editor) return;
      if (linkUrl.trim()) {
        // 检查URL是否已经有协议前缀，如果没有则添加
        let validUrl = linkUrl.trim();
        if (!/^https?:\/\//i.test(validUrl)) {
          validUrl = 'http://' + validUrl;
        }

        // 如果没有选中文本，创建一个链接节点直接插入
        if (editor?.state.selection.empty) {
          // 创建一个包含链接的HTML片段并插入
          const linkText = validUrl.replace(/^https?:\/\/(www\.)?/, ''); // 简化显示的链接文本
          const linkHTML = `<a href="${validUrl}" target="_blank" rel="noopener noreferrer" class="editor-link">${linkText}</a>`;

          editor.chain().focus().insertContent(linkHTML).run();
        } else {
          // 如果有选中文本，将选中的文本转换为链接
          editor
            .chain()
            .focus()
            .setLink({
              href: validUrl,
              target: '_blank',
              rel: 'noopener noreferrer',
            })
            .run();
        }

        // 重置和隐藏输入框
        setLinkUrl('');
        setShowLinkInput(false);
      }
    };
    // 在初始内容变化时更新编辑器
    useEffect(() => {
      if (editor && initialContent && editor.getHTML() !== initialContent) {
        editor.commands.setContent(initialContent);
      }
    }, [initialContent, editor]);

    useEffect(() => {
      if (!editor) return;

      const editorDom = editor.view.dom;

      let startX = 0;
      let startY = 0;
      let startWidth = 0;
      let startHeight = 0;
      let currentImage: HTMLImageElement | null = null;
      let resizeOverlay: HTMLDivElement | null = null;

      function onMouseDown(e: MouseEvent) {
        const target = e.target as HTMLElement;

        // Check if we clicked on an image or its resize handle
        const isImage = target.tagName === 'IMG';
        const isResizeHandle = target.classList.contains('resize-handle');

        const img = isImage
          ? (target as HTMLImageElement)
          : isResizeHandle
            ? (target.parentElement?.querySelector('img') as HTMLImageElement)
            : null;

        if (img) {
          // Check if we're in the bottom-right corner resize area
          const rect = img.getBoundingClientRect();
          const isInBottomRight =
            e.clientX >= rect.right - 20 &&
            e.clientX <= rect.right &&
            e.clientY >= rect.bottom - 20 &&
            e.clientY <= rect.bottom;

          if (isInBottomRight || isResizeHandle) {
            e.preventDefault();

            // Store initial position and size
            startX = e.clientX;
            startY = e.clientY;
            startWidth = img.width || img.offsetWidth;
            startHeight = img.height || img.offsetHeight;
            currentImage = img;

            // Add a class to indicate we're resizing
            document.body.classList.add('resizing-image');

            // Create a visual overlay to indicate resize operation
            if (!resizeOverlay) {
              resizeOverlay = document.createElement('div');
              resizeOverlay.style.position = 'fixed';
              resizeOverlay.style.top = '0';
              resizeOverlay.style.left = '0';
              resizeOverlay.style.right = '0';
              resizeOverlay.style.bottom = '0';
              resizeOverlay.style.zIndex = '9999';
              resizeOverlay.style.pointerEvents = 'none'; // Allow clicks to pass through
              document.body.appendChild(resizeOverlay);
            }
          }
        }
      }

      function onMouseMove(e: MouseEvent) {
        if (!currentImage || !editor) return;

        // Calculate how much the mouse has moved
        const dx = e.clientX - startX;
        const dy = e.clientY - startY;

        // Maintain aspect ratio
        const aspectRatio = startWidth / startHeight;
        const newWidth = Math.max(50, startWidth + dx);
        const newHeight = Math.max(50, newWidth / aspectRatio);

        // Update image size in the DOM
        currentImage.width = newWidth;
        currentImage.height = newHeight;

        // Also update the style to ensure it's visually updated
        currentImage.style.width = `${newWidth}px`;
        currentImage.style.height = `${newHeight}px`;

        // Update the model
        editor.commands.updateAttributes('image', {
          width: newWidth,
          height: newHeight,
        });
      }

      function onMouseUp() {
        if (currentImage && editor) {
          // 获取调整后的宽度和高度
          const newWidth = currentImage.width;
          const newHeight = currentImage.height;

          // 更新Image节点的width和height属性
          editor.commands.updateAttributes('image', {
            width: newWidth.toString(),
            height: newHeight.toString(),
            // 同时更新内联样式
            style: `width: ${newWidth}px; height: ${newHeight}px;`,
          });

          // 也可以显式更新DOM元素，但通常不需要，因为TipTap会自动更新
          currentImage.width = newWidth;
          currentImage.height = newHeight;
          currentImage.style.width = `${newWidth}px`;
          currentImage.style.height = `${newHeight}px`;

          // 重置状态
          currentImage = null;
          document.body.classList.remove('resizing-image');

          if (resizeOverlay && resizeOverlay.parentNode) {
            resizeOverlay.parentNode.removeChild(resizeOverlay);
            resizeOverlay = null;
          }
        }
      }

      // Add event listeners
      editorDom.addEventListener('mousedown', onMouseDown);
      document.addEventListener('mousemove', onMouseMove);
      document.addEventListener('mouseup', onMouseUp);

      return () => {
        // Clean up
        editorDom.removeEventListener('mousedown', onMouseDown);
        document.removeEventListener('mousemove', onMouseMove);
        document.removeEventListener('mouseup', onMouseUp);
        document.body.classList.remove('resizing-image');

        // Remove the overlay if it exists
        if (resizeOverlay && resizeOverlay.parentNode) {
          resizeOverlay.parentNode.removeChild(resizeOverlay);
        }
      };
    }, [editor]);

    // 使用 useImperativeHandle 暴露编辑器的方法给父组件
    useImperativeHandle(ref, () => ({
      insertImage: (
        src: string,
        title?: string,
        alt?: string,
        width?: number,
        height?: number
      ) => {
        if (editor) {
          editor
            .chain()
            .focus()
            .setImage({
              src,
              title,
              alt,
              // 直接传递width和height作为属性
              width: width ? width.toString() : undefined,
              height: height ? height.toString() : undefined,
              // 同时在HTMLAttributes中指定，以确保兼容性
              HTMLAttributes: {
                width: width ? width.toString() : undefined,
                height: height ? height.toString() : undefined,
                style:
                  width && height
                    ? `width: ${width}px; height: ${height}px;`
                    : undefined,
              },
            } as any)
            .run();
        }
      },
      setContent: (content: string) => {
        if (editor) {
          editor.commands.setContent(content);
        }
      },
      getContent: () => {
        return editor ? editor.getHTML() : '';
      },
      isEmpty: () => {
        return editor ? editor.isEmpty : true;
      },
      insertContentAtCursor: (content: string) => {
        if (editor) {
          // 如果编辑器为空，直接设置内容
          if (editor.isEmpty) {
            editor.commands.setContent(content);
          } else {
            // 否则，在当前光标位置插入内容
            editor.commands.insertContent(content);

            // 插入后保持焦点，确保光标位置正确
            editor.commands.focus();
          }
        }
      },
    }));
    return (
      <div className="email-editor">
        <div className="relative">
          <TiptapToolbar
            editor={editor}
            onAddLink={handleAddLink}
            onAddImage={onAddImage}
          />

          {/* 链接输入界面 - 放在工具栏下方 */}
          {showLinkInput && (
            <div className="absolute top-full left-0 right-0 z-10 bg-white border border-gray-200 p-3 flex items-center shadow-md rounded-b">
              <span className="mr-2 text-sm font-medium">Enter link URL:</span>
              <input
                ref={linkInputRef}
                type="text"
                value={linkUrl}
                onChange={(e) => setLinkUrl(e.target.value)}
                className="flex-1 px-3 py-1.5 border rounded-l focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="https://example.com"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleLinkSubmit();
                  } else if (e.key === 'Escape') {
                    setShowLinkInput(false);
                    setLinkUrl('');
                  }
                }}
              />
              <button
                onClick={handleLinkSubmit}
                className="px-3 py-1.5 bg-blue-500 text-white rounded-r hover:bg-blue-600"
              >
                Confirm
              </button>
              <button
                onClick={() => {
                  setShowLinkInput(false);
                  setLinkUrl('');
                }}
                className="ml-2 px-3 py-1.5 bg-gray-300 text-gray-700 rounded hover:bg-gray-400"
              >
                Cancel
              </button>
            </div>
          )}
        </div>

        <div className="rounded p-4 mt-1">
          <style>{emailEditorStyles}</style>
          <EditorContent editor={editor} />
        </div>
      </div>
    );
  }
);

EmailEditor.displayName = 'EmailEditor';

export default EmailEditor;
