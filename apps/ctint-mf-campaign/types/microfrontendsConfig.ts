// eslint-disable-next-line unused-imports/no-unused-imports

export interface microfrontends {
  'ctint-mf-campaign': CtintMF;
}

export interface CtintMF {
  auditTabNames: never[];
  host: string;
  basepath: null | string;
}

export interface CtintMFBroadcast extends CtintMF {
  'broadcast-tab-names': BroadcastTabName[];
}

export interface BroadcastTabName {
  labelEn: string;
  labelCh: string;
  value: string;
  filterType: string;
  active: boolean;
  readOnly: boolean;
  require: boolean;
}

// 定义请求体接口
export interface CampaignSaveRequest {
  id?: string;
  name?: string;
  from?: string;
  template?: string;
  sendDate?: string;
  status?: string;
  contactGroupId: string;
  optOutGroupId?: string;
  resendDate?: string;
  type?: string;
}

export interface BroadcastData {
  id: string;
  name: string;
  from: string;
  template: string;
  templateName?: string;
  sendDate: string;
  resendDate?: string;
  status: string;
  total?: string;
  sentCount?: string;
  successCount?: string;
  readCount?: string;
  repliedCount?: string;
  errorCount?: string;
  contactGroupId?: string;
  optOutGroupId?: string;
  createTime?: string;
  updateTime?: string;
  createBy?: string;
  updateBy?: string;
  tenant?: string;
  type?: string;
  isDeleted?: string;
  notificationRecipient?: string;
  markEntryFlag?: boolean;
}
