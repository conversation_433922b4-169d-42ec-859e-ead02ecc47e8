import React, { useState, useEffect } from 'react';
import BroadcastHeader from './broadcastHeader';
import {
  getDetail,
  getContactGroupList,
  getOptOutList,
  getMessageTemplateList,
  getJobResultList,
  getMessageTemplateDetail,
  exportCampaignDetail,
} from '../../../lib/api/index';
import { useRouteH<PERSON>ler, Toaster, toast } from '@cdss-modules/design-system';
import dayjs from 'dayjs';
import { BroadcastData } from '../../../types/microfrontendsConfig';
import TemplateDetailPreview, { TemplateData } from './messagePreview';
import { DataTable } from '@cdss-modules/design-system/components/_ui/DataTable';
import { Pagination } from '@cdss-modules/design-system/components/_ui/Pagination';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';

export const BroadcastView = () => {
  const [loading, setLoading] = useState(false);
  const [broadcastData, setBroadcastData] = useState({
    broadcastName: '-',
    from: '-',
    messageTemplate: '-',
    date: '-',
    notificationEmail: '-',
    contactGroup: '-',
    optOutGroup: '-',
    markEntryFlag: false,
  });

  const [templateData, setTemplateData] = useState<TemplateData | null>(null);
  const [templateLoading, setTemplateLoading] = useState(false);
  const [messageParameters, setMessageParameters] = useState<any[]>([]);
  const [searchKeyword, setSearchKeyword] = useState('');

  // 分页状态 - 完全按照 index.tsx 的模式
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
    totalPage: 0,
  });

  const { basePath, searchParams } = useRouteHandler();
  const broadcastId = searchParams.get('id') || '';

  // 添加一个 useEffect 来监控 messageParameters 的变化
  useEffect(() => {
    console.log('messageParameters 状态变化:', messageParameters);
    console.log('messageParameters 长度:', messageParameters.length);
  }, [messageParameters]);

  const loadMessageTemplateDetail = async (templateId: string) => {
    try {
      setTemplateLoading(true);
      const templateResponse = await getMessageTemplateDetail(
        templateId,
        basePath
      );

      if (templateResponse.data?.data) {
        setTemplateData(templateResponse.data.data);
      } else {
        setTemplateData(null);
      }
    } catch (error) {
      console.error('Error fetching template details:', error);
      setTemplateData(null);
    } finally {
      setTemplateLoading(false);
    }
  };

  // 获取消息参数数据 - 完全按照 index.tsx 的 fetchBroadcasts 模式
  const fetchMessageParameters = async (page = 1, pageSize = 10) => {
    console.log('=== fetchMessageParameters 开始 ===');
    console.log('参数:', {
      page,
      pageSize,
      broadcastId,
      searchKeyword,
      basePath,
    });

    if (!broadcastId) {
      console.log('broadcastId 为空，跳过获取消息参数');
      return;
    }

    setLoading(true);

    try {
      const response = await getJobResultList(
        broadcastId,
        page,
        pageSize,
        'createTime',
        'desc',
        basePath,
        searchKeyword
      );

      console.log('原始响应:', response);

      // 检查响应是否有效 - 按照 index.tsx 的逻辑
      if (response && response.data) {
        // 根据API返回的结构获取数据
        let results = [];
        let totalCount = 0;

        // 判断数据结构 - 按照 index.tsx 的判断逻辑
        if (response.data.data) {
          // 如果有嵌套的 data 结构
          results = response.data.data.results || [];
          totalCount = response.data.data.totalCount || 0;
        } else if (response.data.results) {
          // 如果直接在 response.data 中
          results = response.data.results || [];
          totalCount = response.data.totalCount || 0;
        }

        console.log('解析后的数据:');
        console.log('results:', results);
        console.log('totalCount:', totalCount);
        console.log('results 类型:', typeof results);
        console.log('results 是数组:', Array.isArray(results));

        // 确保数据是数组 - 按照 index.tsx 的逻辑，即使没有数据也要设置空数组
        if (Array.isArray(results)) {
          console.log('准备设置 messageParameters，数据:', results);
          console.log('设置前的 messageParameters:', messageParameters);

          setMessageParameters(results || []);

          // 使用 setTimeout 来检查状态是否真的更新了
          setTimeout(() => {
            console.log('设置后的 messageParameters 应该是:', results);
          }, 100);

          // 设置分页信息 - 按照 index.tsx 的分页逻辑，确保 pageSize 是数字
          setPagination({
            current: page,
            pageSize: Number(pageSize),
            total: totalCount,
            totalPage: Math.ceil(totalCount / Number(pageSize)),
          });
        } else {
          console.log('results 不是数组，设置为空数组');
          setMessageParameters([]);
          setPagination({
            current: page,
            pageSize: Number(pageSize),
            total: 0,
            totalPage: 0,
          });
        }
      } else {
        console.log('响应无效或没有数据');
        setMessageParameters([]);
        setPagination({
          current: page,
          pageSize: Number(pageSize),
          total: 0,
          totalPage: 0,
        });
      }
    } catch (error) {
      console.error('获取消息参数数据失败:', error);
      setMessageParameters([]);
      toast({
        title: 'Error',
        description: 'Failed to fetch message parameters',
        variant: 'error',
      });
    } finally {
      setLoading(false);
      console.log('=== fetchMessageParameters 结束 ===');
    }
  };

  useEffect(() => {
    const fetchBroadcastData = async () => {
      if (!broadcastId) {
        return;
      }

      setLoading(true);
      let broadcastDetail: BroadcastData = {} as BroadcastData;

      try {
        const detailResponse = await getDetail(broadcastId, basePath);
        broadcastDetail = detailResponse.data?.data || ({} as BroadcastData);

        const formattedDate = broadcastDetail.sendDate
          ? dayjs(broadcastDetail.sendDate).format('YYYY-MM-DD HH:mm')
          : '';

        setBroadcastData((prevData) => ({
          ...prevData,
          broadcastName: broadcastDetail.name || '-',
          from: broadcastDetail.from || '-',
          date: formattedDate,
          messageTemplate: broadcastDetail.templateName || '-',
          notificationEmail: broadcastDetail.notificationRecipient || '-',
          markEntryFlag: broadcastDetail.markEntryFlag || false,
        }));

        if (broadcastDetail.template) {
          try {
            if (typeof broadcastDetail.template === 'string') {
              const parsedTemplateData = JSON.parse(broadcastDetail.template);
              setTemplateData(parsedTemplateData);
            } else if (typeof broadcastDetail.template === 'object') {
              setTemplateData(broadcastDetail.template);
            } else {
              await loadMessageTemplateDetail(broadcastDetail.template);
            }
          } catch (error) {
            console.error('Error parsing template data:', error);
            if (broadcastDetail.template) {
              await loadMessageTemplateDetail(broadcastDetail.template);
            }
          }
        }
      } catch (error) {
        console.error('获取广播详情失败:', error);
        toast({
          title: 'Error',
          description: 'Failed to load broadcast details',
          variant: 'error',
        });
        setLoading(false);
        return;
      }

      const fetchAllSupportingData = async () => {
        try {
          const [contactGroupResponse, optOutResponse, templateResponse] =
            await Promise.allSettled([
              getContactGroupList(basePath),
              getOptOutList(basePath),
              getMessageTemplateList(basePath),
            ]);

          if (contactGroupResponse.status === 'fulfilled') {
            const contactGroups = contactGroupResponse.value.data?.data || [];
            const contactGroup = contactGroups.find(
              (group: any) => group.id === broadcastDetail.contactGroupId
            );

            setBroadcastData((prevData) => ({
              ...prevData,
              contactGroup:
                contactGroup?.name || broadcastDetail.contactGroupId || '-',
            }));
          }

          if (optOutResponse.status === 'fulfilled') {
            const optOutGroups = optOutResponse.value.data?.data || [];
            const optOutGroup = optOutGroups.find(
              (group: any) => group.id === broadcastDetail.optOutGroupId
            );

            setBroadcastData((prevData) => ({
              ...prevData,
              optOutGroup: optOutGroup?.name || '-',
            }));
          }

          if (templateResponse.status === 'fulfilled') {
            const templates = templateResponse.value.data?.data || [];
            const template: any = templates.find(
              (tpl: any) => tpl.id === broadcastDetail.template
            );

            if (template) {
              setBroadcastData((prevData) => ({
                ...prevData,
                messageTemplate:
                  template.name || broadcastDetail.templateName || '-',
              }));
            }
          }
        } catch (error) {
          console.error('获取辅助数据过程中发生错误:', error);
        }
      };

      await fetchAllSupportingData();
      setLoading(false);

      // 最后获取消息参数数据
      fetchMessageParameters(1, pagination.pageSize);
    };

    fetchBroadcastData();
  }, [broadcastId, basePath]); // 移除 pagination.pageSize 依赖

  const handleExport = async () => {
    try {
      setLoading(true);
      const response = await exportCampaignDetail(basePath, broadcastId);
      const blob = new Blob([response.data], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const contentDisposition = response.headers['content-disposition'];
      let filename = 'campaign_export.csv';

      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename=(.*?)(;|$)/);
        if (filenameMatch && filenameMatch.length >= 2) {
          filename = filenameMatch[1].replace(/"/g, '').trim();
        }
      }

      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', filename);
      link.style.display = 'none';
      document.body.appendChild(link);
      link.click();

      window.setTimeout(() => {
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      }, 100);

      toast({
        title: 'Success',
        description: 'Export completed successfully',
        variant: 'success',
      });
    } catch (error) {
      console.error('Export failed:', error);
      toast({
        title: 'Export Failed',
        description: 'Failed to export campaign details',
        variant: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    console.log('Search button clicked:', searchKeyword);
    fetchMessageParameters(1, pagination.pageSize);
  };

  // 按照 index.tsx 的 generateColumns 模式生成列定义
  const generateColumns = () => {
    const columnKeys = [
      'destination',
      'status',
      'replyMessage',
      'errorMessage',
      'updateTime',
    ];
    const columnHeaders = [
      'Destination Number',
      'Result',
      'Reply Message',
      'Error Message',
      'Last Update Time',
    ];

    return columnKeys.map((col, index) => ({
      id: col,
      accessorKey: col,
      header: () => (
        <div className="flex items-center">
          <span className="text-[14px]">{columnHeaders[index]}</span>
        </div>
      ),
      cell: ({ row }: any) => {
        const value = row.original[col];

        if (value === null || value === undefined) {
          return <div className="text-center">-</div>;
        }

        // 状态列的特殊格式化
        if (col === 'status') {
          let color = '';
          if (
            value === 'Success' ||
            value === 'SUCCESS' ||
            value === 'accepted'
          ) {
            color = 'text-green-500';
          } else if (
            value === 'Error' ||
            value === 'FAILED' ||
            value === 'Failed'
          ) {
            color = 'text-red-500';
          }
          return (
            <div className="text-center">
              <span className={color}>{value}</span>
            </div>
          );
        }

        // 日期格式化
        if (col === 'updateTime' && value) {
          return (
            <div className="text-center">
              {dayjs(value).format('YYYY-MM-DD HH:mm:ss')}
            </div>
          );
        }

        if (col === 'replyMessage' || col === 'errorMessage') {
          return (
            <div
              className="break-words whitespace-normal text-left p-2"
              style={{ width: '100%' }}
            >
              {String(value)}
            </div>
          );
        }

        return <div className="text-center">{String(value)}</div>;
      },
    }));
  };

  return (
    <div className="w-full bg-gray-50 min-h-screen">
      <BroadcastHeader
        title=""
        onSubmit={handleExport}
        buttonText="Export"
      />

      <div className="px-6 py-4">
        <div className="grid grid-cols-12 gap-6">
          <div className="col-span-12 lg:col-span-4">
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-1">
                  Broadcast Name
                </h3>
                <p className="font-medium">{broadcastData.broadcastName}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-1">Date</h3>
                <p className="font-medium">{broadcastData.date}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-1">
                  Contact Group
                </h3>
                <p className="font-medium">{broadcastData.contactGroup}</p>
              </div>
            </div>
          </div>

          <div className="col-span-12 lg:col-span-4">
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-1">From</h3>
                <p className="font-medium">{broadcastData.from}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-1">
                  Opt-out Group
                </h3>
                <p className="font-medium">{broadcastData.optOutGroup}</p>
              </div>
              {/* 新添加的 ACD 字段 */}
              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-1">ACD</h3>
                <div
                  className={`w-[21px] h-[21px] border-2 rounded-md border-primary-500 flex items-center justify-center cursor-default ${
                    broadcastData.markEntryFlag
                      ? 'bg-primary-500'
                      : 'bg-transparent'
                  }`}
                >
                  {broadcastData.markEntryFlag && (
                    <Icon
                      size={11}
                      name="check"
                      className="text-white"
                    />
                  )}
                </div>
              </div>
            </div>
          </div>

          <div className="col-span-12 lg:col-span-4">
            <div className="mb-4">
              <h3 className="text-sm font-medium text-gray-500 mb-1">
                Message Template
              </h3>
              <p className="font-medium">{broadcastData.messageTemplate}</p>
            </div>
            <TemplateDetailPreview
              templateData={templateData}
              isLoading={templateLoading}
              className="mt-4"
              isEditable={false}
            />
          </div>
        </div>

        <div className="mt-8">
          <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
            <div className="flex justify-between items-center p-4 border-b border-gray-200">
              <h3 className="font-medium text-lg">Message Parameter</h3>
              <div className="relative">
                <input
                  type="text"
                  className="border rounded pl-3 pr-10 py-2 w-64"
                  placeholder="Search..."
                  value={searchKeyword}
                  onChange={(e) => setSearchKeyword(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      handleSearch();
                    }
                  }}
                />
                <div
                  className="absolute inset-y-0 right-0 flex items-center pr-3 cursor-pointer"
                  onClick={handleSearch}
                >
                  <svg
                    className="h-5 w-5 text-gray-400"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
              </div>
            </div>

            {/* DataTable 容器 - 完全按照 index.tsx 的模式 */}
            <div className="broadcast-table-container">
              {/* 添加全局样式覆盖 */}
              <style
                jsx
                global
              >{`
                .broadcast-table-container th {
                  text-align: center !important;
                }
                .broadcast-table-container td {
                  text-align: center !important;
                }
                .broadcast-table-container td > div {
                  display: flex !important;
                  justify-content: center !important;
                  width: 100% !important;
                  text-align: center !important;
                }
              `}</style>
              <DataTable
                data={messageParameters}
                columns={generateColumns()}
                loading={false} // 重要：按照 index.tsx，这里传 false
                emptyMessage="No data Available"
                resize={true}
                // 按照 index.tsx 添加 rowSelection 相关属性
                rowSelection={{}}
                onTableSetUp={(table) => {
                  console.log('表格设置完成:', table);
                  console.log('表格数据:', table.options.data);
                  console.log('表格行数:', table.getRowCount());
                  console.log('表格行模型:', table.getRowModel());
                }}
                // 重要：不传递任何分页相关的配置
              />
            </div>

            {/* 外部分页组件 - 完全按照 index.tsx 的模式 */}
            {pagination.total > 0 && (
              <section className="flex-shrink-0 px-4 py-4 border-t">
                <div>
                  <Pagination
                    total={pagination.totalPage}
                    current={pagination.current}
                    initialPage={1}
                    siblings={1}
                    totalCount={pagination.total}
                    perPage={pagination.pageSize}
                    onChange={(page) => {
                      fetchMessageParameters(page, pagination.pageSize);
                    }}
                    handleOnNext={() => {
                      fetchMessageParameters(
                        pagination.current + 1,
                        pagination.pageSize
                      );
                    }}
                    handleOnPrevious={() => {
                      fetchMessageParameters(
                        pagination.current - 1,
                        pagination.pageSize
                      );
                    }}
                    handlePerPageSetter={(newPageSize) => {
                      fetchMessageParameters(1, newPageSize);
                    }}
                  />
                </div>
              </section>
            )}
          </div>
        </div>
      </div>
      <Toaster />
    </div>
  );
};
